"""
資源池擾亂攻擊 (Resource Pool Disruption Attack) - 半雙工實現

此攻擊模型採用半雙工（Half-Duplex）模式，模擬攻擊者在監聽和攻擊之間交替進行。
攻擊者旨在通過佔用網絡中的「乾淨」資源，迫使其他正常車輛使用干擾較高的資源，
從而降低整體網絡的通信效率和可靠性。

攻擊邏輯：
1.  **監聽週期 (100ms)**: 攻擊者在此期間進入純監聽模式，不發送任何信號。
    它會利用其接收器 (`receiver_process`) 持續監測資源池中的所有活動，
    並記錄下在每個資源 `(slot, subchannel)` 上觀測到的所有傳輸信號。

2.  **分析與選擇**: 監聽週期結束後，攻擊者會立即分析收集到的數據。
    - 它會識別出在過去 100ms 內完全沒有任何信號傳輸的「乾淨」資源。
    - 根據您的要求，對於每個有乾淨資源的相對時槽（relative slot），它會從中隨機選擇一個乾淨的子通道（subchannel）。
    - 這些被選中的 `(relative_slot, subchannel)` 將構成下一個攻擊週期的攻擊目標列表。

3.  **攻擊週期 (100ms)**: 根據上一週期生成的攻擊列表，攻擊者在此期間進入純攻擊模式。
    - 它會暫停監聽功能。
    - 對於列表中的每一個目標，它會在當前攻擊週期對應的相對時間點上發送一個短暫的干擾信號（持續1ms），以佔用該資源。

4.  **週期性重複**: 不斷重複上述「監聽 -> 分析 -> 攻擊」的循環。
"""

import random
import simpy
from src.CONFIG import *

class ResourcePoolDisruptorUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
                 initial_pos=(0,0), initial_speed=0, 
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM,
                 ue_specific_logger=None):
        # 基本UE屬性初始化
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger
        self.ue_logger = ue_specific_logger
        self.mobility_model = mobility_model
        
        self.pos = initial_pos
        self.speed = initial_speed
        
        self.is_attacker = True
        self.all_other_ues = {}
        
        # 攻擊者特定參數
        self.jamming_power_dbm = jamming_power_dbm
        # 儲存監聽到的資源使用情況 {resource: total_rsrp}
        self.sensed_resource_rsrp = {}
        # 儲存下一攻擊週期要攻擊的資源候選列表 (relative_slot, subchannel)
        self.attack_candidates = []
        # 半雙工狀態旗標
        self.is_listening = False

        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_DISRUPTOR_INIT", UE_ID=self.ue_id, 
                              Details=f"Jamming Power: {self.jamming_power_dbm}dBm. Using correct half-duplex logic.")
        
        # 啟動攻擊者的運行邏輯和接收器進程
        self.action = env.process(self.attacker_run_logic())
        self.receiver_process_action = env.process(self.receiver_process())

    def attacker_run_logic(self):
        """攻擊者的主要運行邏輯，實現半雙工方案。"""
        self.global_logger.log_event(Time_ms=self.env.now, EventType="ATTACKER_LOGIC_START", UE_ID=self.ue_id)
        
        while True:
            # 1. 監聽與分析階段
            yield self.env.process(self.listen_and_analyze_phase())
            # 2. 攻擊階段
            yield self.env.process(self.attack_phase())

    def listen_and_analyze_phase(self):
        """監聽與分析階段 (100ms): 監聽信道以尋找下一攻擊階段的乾淨資源。"""
        listen_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=listen_phase_start_time, EventType="ATTACKER_LISTEN_PHASE_START", UE_ID=self.ue_id)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_start_time, UE_ID=self.ue_id, EventType="ATTACKER_LISTEN_START")

        # 清空先前的感知數據並開始監聽
        self.sensed_resource_rsrp.clear()
        self.is_listening = True
        
        # 監聽100ms
        yield self.env.timeout(100)
        
        # 停止監聽並開始分析
        self.is_listening = False
        listen_phase_end_time = self.env.now
        
        # --- 分析子階段 ---
        # 找出在監聽窗口內所有被使用過的資源
        used_resources_relative = set()
        for (absolute_slot, subchannel), rsrp in self.sensed_resource_rsrp.items():
            # 雖然 receiver_process 已經過濾了時間，這裡可以再做一次確認
            relative_slot = absolute_slot % 100 # 將絕對時間轉換為0-99的相對時槽
            used_resources_relative.add((relative_slot, subchannel))

        # 識別窗口中每個時槽所有未使用的（「乾淨」）子信道
        clean_subchannels_by_slot = {}
        for slot in range(100): # 遍歷100個相對時槽
            all_subchannels = set(range(NUM_SUBCHANNELS))
            used_subchannels_in_slot = {subchannel for (s, subchannel) in used_resources_relative if s == slot}
            clean_subchannels = list(all_subchannels - used_subchannels_in_slot)
            if clean_subchannels:
                clean_subchannels_by_slot[slot] = clean_subchannels

        # 為下一個週期準備攻擊候選：每個有乾淨資源的時槽，隨機選一個子通道
        self.attack_candidates = []
        for slot, subchannels in clean_subchannels_by_slot.items():
            chosen_subchannel = random.choice(subchannels)
            self.attack_candidates.append((slot, chosen_subchannel))
        
        self.global_logger.log_event(Time_ms=listen_phase_end_time, EventType="ATTACKER_ANALYSIS_COMPLETE", 
                                      UE_ID=self.ue_id, Details=f"Found {len(self.attack_candidates)} candidates for the next attack cycle.")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_end_time, UE_ID=self.ue_id, EventType="ATTACKER_CANDIDATES_SELECTED", 
                                     Details=f"Candidates: {str(self.attack_candidates[:5])}...")

    def attack_phase(self):
        """攻擊階段 (100ms): 在前一監聽階段確定為「乾淨」的資源上傳輸干擾信號。"""
        attack_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=attack_phase_start_time, EventType="ATTACKER_ATTACK_PHASE_START", 
                                      UE_ID=self.ue_id, Details=f"Attacking {len(self.attack_candidates)} resources.")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=attack_phase_start_time, UE_ID=self.ue_id, EventType="ATTACKER_ATTACK_START", 
                                     Details=f"Num targets: {len(self.attack_candidates)}")

        if self.attack_candidates:
            for relative_slot, subchannel in self.attack_candidates:
                # 計算攻擊的絕對時間點
                attack_time = attack_phase_start_time + relative_slot
                # 使用一個獨立的 process 來處理未來的攻擊事件，避免阻塞主循環
                self.env.process(self.jam_resource(attack_time, subchannel))
        
        # 等待攻擊階段完成
        yield self.env.timeout(100)
        self.global_logger.log_event(Time_ms=self.env.now, EventType="ATTACKER_ATTACK_PHASE_END", UE_ID=self.ue_id)

    def jam_resource(self, attack_time, subchannel):
        """在指定的時間點對指定的子通道執行一次干擾。"""
        # 等待直到指定的攻擊時間
        time_to_wait = attack_time - self.env.now
        if time_to_wait > 0:
            yield self.env.timeout(time_to_wait)
        
        # 執行干擾
        current_time = self.env.now
        # 在這個時間點，slot 就是 current_time
        resource_to_attack = (current_time, subchannel)
        
        self.resource_pool.log_jamming(self.ue_id, resource_to_attack, self.jamming_power_dbm, 1) # 干擾持續1ms
        
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                                    EventType="ATTACKER_JAM_EXECUTE", 
                                    Resource_Details=str(resource_to_attack), 
                                    Details=f"Power: {self.jamming_power_dbm}dBm")

    def receiver_process(self):
        """持續監聽其他UE的傳輸。只有在 is_listening 為 True 時才記錄數據。"""
        while True:
            # 以較高的頻率輪詢以捕捉所有傳輸
            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
            
            if not self.is_listening:
                continue

            current_time = self.env.now
            # 更新自身位置
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]
            
            active_transmissions_copy = dict(self.resource_pool.active_transmissions)
            
            for trans_key, trans_info in active_transmissions_copy.items():
                # 檢查傳輸是否在當前輪詢時間點附近活躍
                if not (trans_info["start_time"] <= current_time < trans_info["end_time"]):
                    continue
                
                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id:
                    continue
                
                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]
                
                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object:
                    continue
                
                distance = self.comm_model.calculate_distance(self.pos, sender_ue_object.pos)
                if distance > MAX_COMM_RANGE_M:
                    continue
                
                received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if received_power_dbm < MIN_RX_POWER_DBM:
                    continue
                
                # 記錄感知到的資源使用情況（無論信號類型）
                # 我們只關心資源是否被佔用，所以將所有接收到的功率累加
                # 注意：在真實的物理層中，這應該是線性功率的累加，但為簡化，我們在此僅記錄事件
                # if resource not in self.sensed_resource_rsrp and trans_info["type"] == "SCI":
                if resource not in self.sensed_resource_rsrp:
                    self.sensed_resource_rsrp[resource] = []
                # self.sensed_resource_rsrp[resource].append(received_power_dbm)
                self.sensed_resource_rsrp[resource] = received_power_dbm
                print(self.sensed_resource_rsrp)

