from src.CONFIG import *
from src.ue import DEFAULT_PRSRP

class CriticalMessageAttackerUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
                 initial_pos=(0,0), initial_speed=0, 
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM, jamming_duration_ms=ATTACKER_JAMMING_DURATION_MS,
                 ue_specific_logger=None):
        # 繼承基本UE類別的初始化
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger
        self.ue_logger = ue_specific_logger
        self.mobility_model = mobility_model
        
        self.pos = initial_pos
        self.speed = initial_speed
        self.direction_rad = 0
        
        self.prsvp = DEFAULT_PRSRP
        self.slrrc = 0 
        self.slrrc_max = DEFAULT_SLRRC_MAX 
        self.slrrc_min = DEFAULT_SLRRC_MIN
        self.nmax_reserve = DEFAULT_NMAX_RESERVE
        self.resource_keep_probability = DEFAULT_RESOURCE_KEEP_PROBABILITY
        
        self.selected_resources = [] 
        self.sensed_resource_map = {} 
        self.data_queue = [] 
        self.is_attacker = True
        self.all_other_ues = {}
        
        # 攻擊者特有參數
        self.jamming_power_dbm = jamming_power_dbm
        self.jamming_duration_ms = jamming_duration_ms
        self.critical_message_types = ["EMERGENCY_BRAKE", "LANE_CHANGE", "COLLISION_WARNING"]
        self.monitored_ues = {}  # 記錄監聽到的車輛及其資源使用情況
        self.attack_cooldown = 0  # 攻擊冷卻時間
        
        self.global_logger.log_event(Time_ms=self.env.now, EventType="CRITICAL_ATTACKER_INIT", UE_ID=self.ue_id, 
                              Details=f"Jamming Power: {self.jamming_power_dbm}dBm. Monitoring for critical messages.")
        
        # 啟動攻擊者的運行邏輯
        self.action = env.process(self.attacker_run_logic())
        self.receiver_process_action = env.process(self.receiver_process())
        self.victim_record = {} #{ue_id:resource}
        self.victim_emergency_bsm_counter = {}
        
    def attacker_run_logic(self):
            """攻擊者的主要運行邏輯，現在只負責執行干擾"""
            self.global_logger.log_event(Time_ms=self.env.now, EventType="CRITICAL_ATTACKER_LOGIC_START", UE_ID=self.ue_id)
            if self.ue_logger:
                self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="CRITICAL_ATTACKER_PROCESS_START")
            
            while True:
                # 遍歷當前的受害者列表
                # 使用 list(self.victim_record.items()) 是為了安全地遍歷，以防未來可能在循環中修改字典
                print(self.env.now, self.victim_record)
                for ue_id, resource in list(self.victim_record.items()):
                    # print(ue_id, resource)
                    # 在正確的 slot 執行干擾
                    if self.env.now % 100 == resource[0]:
                        self.execute_jamming(ue_id, resource)
                
                # 仍然以 1ms 的頻率檢查，以確保能精準地在 slot 開始時干擾
                yield self.env.timeout(SLOT_DURATION_MS)
    
    
    def execute_jamming(self, target_ue_id, target_resource):
        """執行對關鍵訊息的干擾"""
        current_time = self.env.now
        
        self.global_logger.log_event(Time_ms=current_time, EventType="CRITICAL_ATTACKER_JAM_START", 
                              UE_ID=self.ue_id, Target_UE_ID=target_ue_id, 
                              Resource=str(target_resource), Power_dBm=self.jamming_power_dbm,
                              Details=f"Jamming critical message")
        
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                                    EventType="CRITICAL_ATTACKER_JAM_EXECUTE", 
                                    Resource_Details=str(target_resource), 
                                    Related_UE_ID=target_ue_id, 
                                    Details=f"Power: {self.jamming_power_dbm}dBm, Message")
        
        # 對目標資源進行干擾
        if target_resource:
            self.resource_pool.log_jamming(self.ue_id, target_resource, self.jamming_power_dbm, self.jamming_duration_ms)
    
    def receiver_process(self):
        """接收器處理，監聽周圍車輛的訊息"""
        while True:
            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
            current_time = self.env.now
            
            # 更新自身位置和速度
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]
            self.speed = current_mobility_state["speed_mps"]
            
            # 獲取當前活躍的傳輸
            active_transmissions_copy = dict(self.resource_pool.active_transmissions)
            
            for trans_key, trans_info in active_transmissions_copy.items():
                if not (trans_info["start_time"] <= current_time <= trans_info["end_time"]):
                    continue
                
                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id:
                    continue
                
                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]
                signal_type = trans_info["type"]
                content = trans_info["content"]
                
                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object:
                    continue
                
                sender_pos = sender_ue_object.pos
                
                # 計算距離並檢查是否在通信範圍內
                distance = self.comm_model.calculate_distance(self.pos, sender_pos)
                if distance > MAX_COMM_RANGE_M:
                    continue
                
                # 計算接收功率並檢查是否超過最小接收閾值
                received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if received_power_dbm < MIN_RX_POWER_DBM:
                    continue
                
                # 更新監聽到的車輛信息
                if sender_id not in self.monitored_ues:
                    self.monitored_ues[sender_id] = {"resource": None, "last_message": None}
                
                # 更新資源使用情況 - 確保資源是tuple類型
                self.monitored_ues[sender_id]["resource"] = resource
                # 更新最近接收到的訊息
                if signal_type == "PSSCH" and content:
                    is_critical = content.get("is_emergency", False)

                    if is_critical:    
                        # 如果是緊急訊息，立即更新受害者記錄
                        # 只有在目標改變時才打印日誌，避免刷屏
                        if self.victim_record.get(sender_id) != resource:
                            self.global_logger.log_event(Time_ms=current_time, EventType="CRITICAL_ATTACKER_TARGET_DETECTED", 
                                                          UE_ID=self.ue_id, Target_UE_ID=sender_id,
                                                          Details=f"Critical message detected on resource {resource}!",
                                                          PSSCH_Content_Abstract=str(content))
                            if self.ue_logger:
                                self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                                                        EventType="CRITICAL_ATTACKER_TARGET_DETECTED",
                                                        Related_UE_ID=sender_id, 
                                                        Details=f"Critical message detected!",
                                                        PSSCH_Content_Abstract=str(content))
                        
                        self.victim_record[sender_id] = resource
                        self.victim_emergency_bsm_counter[sender_id] = 0
                    elif is_critical == False:
                        if self.victim_emergency_bsm_counter.get(sender_id) is not None and self.victim_emergency_bsm_counter[sender_id] > 1:     
                            self.victim_record.pop(sender_id, None)
                            self.victim_emergency_bsm_counter.pop(sender_id, None)
                        elif self.victim_emergency_bsm_counter.get(sender_id) is not None:
                            self.victim_emergency_bsm_counter[sender_id] += 1 

                    

                    # self.monitored_ues[sender_id]["last_message"] = content
                    
                    # # 記錄監聽到的訊息
                    # self.global_logger.log_event(Time_ms=current_time, EventType="CRITICAL_ATTACKER_MESSAGE_MONITORED", 
                    #                       UE_ID=self.ue_id, Target_UE_ID=sender_id,
                    #                       Content_Abstract=str(content))
                    
                    # if self.ue_logger:
                    #     self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                    #                             EventType="CRITICAL_ATTACKER_MESSAGE_MONITORED", 
                    #                             Related_UE_ID=sender_id, 
                    #                             PSSCH_Content_Abstract=str(content))