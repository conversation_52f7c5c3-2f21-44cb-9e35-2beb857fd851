"""
資源池擾亂攻擊 (Resource Pool Disruption Attack)

此攻擊方法試圖擾亂整個通信資源池的使用效率，迫使合法車輛在不利的通信條件下運行。
攻擊者通過佔用相對「乾淨」(干擾較小) 的通信資源，迫使其他車輛選擇較為「嘈雜」(干擾較大) 的資源，
增加訊息碰撞和接收失敗的概率。

攻擊步驟：
1. 監聽階段：觀察資源使用情況，確定哪些是相對空閒(乾淨)的資源
2. 攻擊階段：在識別出的相對乾淨的資源上發送一階SCI訊息，佔用這些資源
"""

import random
from src.CONFIG import *
from src.ue import VehicleUE, DEFAULT_PRSRP
from src.mobility import MODEL_LINEAR
from src.logger import Logger # Assuming Logger class is in logger.py
from src.simulators.simulator import NRV2XSimulator
from src.attacker_model.resource_pool_disruptor import ResourcePoolDisruptorUE


# class ResourcePoolDisruptorUE:
#     def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
#                  initial_pos=(0,0), initial_speed=0, 
#                  jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM, jamming_duration_ms=ATTACKER_JAMMING_DURATION_MS,
#                  ue_specific_logger=None):
#         # 繼承基本UE類別的初始化
#         self.env = env
#         self.ue_id = ue_id
#         self.resource_pool = resource_pool
#         self.comm_model = comm_model
#         self.global_logger = global_logger
#         self.ue_logger = ue_specific_logger
#         self.mobility_model = mobility_model
        
#         self.pos = initial_pos
#         self.speed = initial_speed
#         self.direction_rad = 0
        
#         self.prsvp = DEFAULT_PRSRP
#         self.slrrc = 0 
#         self.slrrc_max = DEFAULT_SLRRC_MAX 
#         self.slrrc_min = DEFAULT_SLRRC_MIN
#         self.nmax_reserve = DEFAULT_NMAX_RESERVE
#         self.resource_keep_probability = DEFAULT_RESOURCE_KEEP_PROBABILITY
        
#         self.selected_resources = [] 
#         self.sensed_resource_map = {} 
#         self.data_queue = [] 
#         self.is_attacker = True
#         self.all_other_ues = {}
        
#         # 攻擊者特有參數
#         self.jamming_power_dbm = jamming_power_dbm
#         self.jamming_duration_ms = jamming_duration_ms
#         self.resource_quality_map = {}  # 記錄各資源的品質評估
#         self.clean_resources = []  # 記錄相對乾淨的資源
#         self.occupied_resources = []  # 記錄已佔用的資源
#         self.observation_period = 100  # 觀察期（毫秒）
#         self.attack_cycle_duration = 100  # 攻擊週期（毫秒）
        
#         self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_DISRUPTOR_INIT", UE_ID=self.ue_id, 
#                               Details=f"Jamming Power: {self.jamming_power_dbm}dBm. Targeting clean resources.")
        
#         # 啟動攻擊者的運行邏輯
#         self.action = env.process(self.attacker_run_logic())
#         self.receiver_process_action = env.process(self.receiver_process())
        
#     def attacker_run_logic(self):
#         """攻擊者的主要運行邏輯"""
#         self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_DISRUPTOR_LOGIC_START", UE_ID=self.ue_id)
#         if self.ue_logger:
#             self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="RESOURCE_DISRUPTOR_PROCESS_START")
        
#         # 初始觀察期
#         yield self.env.timeout(self.observation_period)
        
#         while True:
#             current_time = self.env.now
            
#             # 監聽階段：分析資源使用情況
#             self.analyze_resource_quality()
            
#             # 攻擊階段：佔用相對乾淨的資源
#             self.occupy_clean_resources()
            
#             # 等待一個攻擊週期
#             yield self.env.timeout(self.attack_cycle_duration)
            
#             # 釋放之前佔用的資源，準備下一輪攻擊
#             self.release_occupied_resources()
    
#     def analyze_resource_quality(self):
#         """分析各資源的品質，識別相對乾淨的資源"""
#         current_time = self.env.now
        
#         # 重置資源品質評估
#         self.resource_quality_map = {}
        
#         # 遍歷所有可能的資源（時隙和子通道組合）
#         for slot in range(100):  # 假設TTI為100ms，每個時隙1ms
#             for subchannel in range(NUM_SUBCHANNELS):
#                 resource = (slot, subchannel)
                
#                 # 評估資源品質（干擾水平）
#                 interference_level = self.estimate_resource_interference(resource)
                
#                 # 記錄資源品質
#                 self.resource_quality_map[resource] = interference_level
        
#         # 根據干擾水平排序資源
#         sorted_resources = sorted(self.resource_quality_map.items(), key=lambda x: x[1])
        
#         # 選擇最乾淨的20%資源
#         clean_resources_count = int(len(sorted_resources) * 0.2)
#         self.clean_resources = [res[0] for res in sorted_resources[:clean_resources_count]]
        
#         self.global_logger.log_event(Time_ms=current_time, EventType="RESOURCE_DISRUPTOR_ANALYSIS_COMPLETE", 
#                               UE_ID=self.ue_id, Details=f"Identified {len(self.clean_resources)} clean resources")
        
#         if self.ue_logger:
#             self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
#                                     EventType="RESOURCE_DISRUPTOR_ANALYSIS_COMPLETE", 
#                                     Details=f"Clean resources: {str(self.clean_resources[:5])}...")
    
#     def estimate_resource_interference(self, resource):
#         """估計特定資源的干擾水平"""
#         # 在實際情況中，這裡會有更複雜的邏輯來評估干擾
#         # 這裡我們使用一個簡化的模擬：基於sensed_resource_map中的記錄評估干擾
        
#         interference_level = 0
        
#         # 檢查是否有其他UE在使用或預留此資源
#         for res_key, info in self.sensed_resource_map.items():
#             if res_key == resource:
#                 interference_level += 10  # 直接使用會有高干擾
#             elif res_key[0] == resource[0]:  # 同一時隙的不同子通道
#                 interference_level += 5  # 時隙衝突有中等干擾
#             elif res_key[1] == resource[1]:  # 同一子通道的不同時隙
#                 interference_level += 2  # 子通道衝突有低干擾
        
#         # 加入一些隨機因素模擬實際環境中的變化
#         interference_level += random.uniform(0, 3)
        
#         return interference_level
    
#     def occupy_clean_resources(self):
#         """佔用相對乾淨的資源"""
#         current_time = self.env.now
        
#         # 根據Nmax_reserve限制選擇要佔用的資源數量
#         resources_to_occupy = min(len(self.clean_resources), self.nmax_reserve)
        
#         if resources_to_occupy == 0:
#             self.global_logger.log_event(Time_ms=current_time, EventType="RESOURCE_DISRUPTOR_NO_CLEAN_RESOURCES", 
#                                   UE_ID=self.ue_id, Details="No clean resources available to occupy")
#             return
        
#         # 從乾淨資源中隨機選擇要佔用的資源
#         selected_resources = random.sample(self.clean_resources, resources_to_occupy)
        
#         # 佔用選定的資源
#         for resource in selected_resources:
#             self.global_logger.log_event(Time_ms=current_time, EventType="RESOURCE_DISRUPTOR_OCCUPY_RESOURCE", 
#                                   UE_ID=self.ue_id, Resource=str(resource), Power_dBm=self.jamming_power_dbm,
#                                   Details="Occupying clean resource")
            
#             if self.ue_logger:
#                 self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
#                                         EventType="RESOURCE_DISRUPTOR_OCCUPY_RESOURCE", 
#                                         Resource_Details=str(resource), 
#                                         Details=f"Power: {self.jamming_power_dbm}dBm")
            
#             # 在資源上發送SCI訊息（佔用資源）
#             self.resource_pool.log_jamming(self.ue_id, resource, self.jamming_power_dbm, self.attack_cycle_duration)
            
#             # 記錄已佔用的資源
#             self.occupied_resources.append(resource)
        
#         self.global_logger.log_event(Time_ms=current_time, EventType="RESOURCE_DISRUPTOR_RESOURCES_OCCUPIED", 
#                               UE_ID=self.ue_id, Details=f"Occupied {len(selected_resources)} clean resources")
    
#     def release_occupied_resources(self):
#         """釋放之前佔用的資源"""
#         current_time = self.env.now
        
#         if not self.occupied_resources:
#             return
        
#         self.global_logger.log_event(Time_ms=current_time, EventType="RESOURCE_DISRUPTOR_RESOURCES_RELEASED", 
#                               UE_ID=self.ue_id, Details=f"Released {len(self.occupied_resources)} occupied resources")
        
#         # 清空已佔用資源列表
#         self.occupied_resources = []
    
#     def receiver_process(self):
#         """接收器處理，監聽周圍車輛的訊息和資源使用情況"""
#         while True:
#             yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
#             current_time = self.env.now
            
#             # 更新自身位置和速度
#             current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
#             self.pos = current_mobility_state["pos"]
#             self.speed = current_mobility_state["speed_mps"]
            
#             # 獲取當前活躍的傳輸
#             active_transmissions_copy = dict(self.resource_pool.active_transmissions)
            
#             # 清空舊的資源感知記錄
#             self.sensed_resource_map = {}
            
#             for trans_key, trans_info in active_transmissions_copy.items():
#                 if not (trans_info["start_time"] <= current_time < trans_info["end_time"]):
#                     continue
                
#                 sender_id = trans_info["ue_id"]
#                 if sender_id == self.ue_id:
#                     continue
                
#                 resource = trans_info["resource"]
#                 tx_power_dbm = trans_info["tx_power"]
#                 signal_type = trans_info["type"]
                
#                 sender_ue_object = self.all_other_ues.get(sender_id)
#                 if not sender_ue_object:
#                     continue
                
#                 sender_pos = sender_ue_object.pos
                
#                 # 計算距離並檢查是否在通信範圍內
#                 distance = self.comm_model.calculate_distance(self.pos, sender_pos)
#                 if distance > MAX_COMM_RANGE_M:
#                     continue
                
#                 # 計算接收功率並檢查是否超過最小接收閾值
#                 received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
#                 if received_power_dbm < MIN_RX_POWER_DBM:
#                     continue
                
#                 # 記錄感知到的資源使用情況
#                 self.sensed_resource_map[resource] = {
#                     "sender_id": sender_id,
#                     "signal_type": signal_type,
#                     "rsrp": received_power_dbm,
#                     "timestamp": current_time
#                 }
                
#                 # 記錄監聽到的資源使用
#                 if self.ue_logger and random.random() < 0.1:  # 只記錄10%的監聽事件，避免日誌過大
#                     self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
#                                             EventType="RESOURCE_DISRUPTOR_RESOURCE_SENSED", 
#                                             Resource_Details=str(resource), 
#                                             Related_UE_ID=sender_id, 
#                                             RSRP_dBm=received_power_dbm)


class Simulator3(NRV2XSimulator):
        def __init__(self, simulation_index):
            super().__init__(simulation_index)
        def setup_scenario(self):
            self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_START", UE_ID="System")
            print(f"Time {self.env.now}: Setting up scenario...")

            # 創建正常車輛
            for i in range(NUM_NORMAL_UES):
                ue_id = f"NormalUE-{i}"
                initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
                initial_dir_deg = 90
                initial_speed_mps = random.uniform(8, 15)
                
                # 創建UE特定日誌
                ue_logger_instance = Logger(filename=f"{ue_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
                self.ue_loggers.append(ue_logger_instance)
                
                ue = VehicleUE(self.env, ue_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                               initial_pos=initial_pos, initial_speed=initial_speed_mps,
                               prsvp=PRSVP, ue_specific_logger=ue_logger_instance)
                self.normal_ues.append(ue)
                self.all_ues.append(ue)
                self.ues_objects_dict[ue_id] = ue
                self.mobility_model.initialize_ue_state(ue_id, model_type=MODEL_LINEAR, 
                                                       initial_pos=initial_pos, 
                                                       initial_speed_mps=initial_speed_mps, 
                                                       initial_direction_deg=initial_dir_deg)
                self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=ue_id, Details="Type: Normal")
                print(f"  {ue_id} created.")

            # 創建資源池擾亂攻擊者
            for i in range(NUM_ATTACKER_UES):
                attacker_id = f"ResourceDisruptorUE-{i}"
                initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
                initial_dir_deg = random.uniform(0, 360)
                initial_speed_mps = random.uniform(10, 12)
                
                # 創建攻擊者特定日誌
                
                attacker_logger_instance = Logger(filename=f"{attacker_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
                self.ue_loggers.append(attacker_logger_instance)
                
                # 使用ResourcePoolDisruptorUE類別
                attacker = ResourcePoolDisruptorUE(self.env, attacker_id, self.resource_pool, self.comm_model, 
                                                 self.global_logger, self.mobility_model,
                                                 initial_pos=initial_pos, initial_speed=initial_speed_mps,
                                                 ue_specific_logger=attacker_logger_instance)
                self.attacker_ues.append(attacker)
                self.all_ues.append(attacker)
                self.ues_objects_dict[attacker_id] = attacker
                self.mobility_model.initialize_ue_state(attacker_id, model_type=MODEL_LINEAR, 
                                                         initial_pos=initial_pos, 
                                                         initial_speed_mps=initial_speed_mps, 
                                                         initial_direction_deg=initial_dir_deg)
                self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=attacker_id, Details=f"Type: ResourceDisruptor")
                print(f"  {attacker_id} created.")

            for ue in self.all_ues:
                ue.all_other_ues = {other_ue.ue_id: other_ue for other_ue in self.all_ues if other_ue.ue_id != ue.ue_id}
                ue.comm_model = self.comm_model 
                ue.resource_pool = self.resource_pool

            self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_END", UE_ID="System")
            print(f"Time {self.env.now}: Scenario setup complete. Total UEs: {len(self.all_ues)}")
    