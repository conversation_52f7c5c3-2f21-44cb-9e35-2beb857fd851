# ue.py
import simpy
import random
import math
from src.CONFIG import *

# Assuming Logger class is available if ue_specific_logger is used.
# from logger import Logger # Not strictly needed here if logger instance is passed

class VehicleUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
                 initial_pos=(0,0), initial_speed=0, 
                 prsvp=DEFAULT_PRSRP, 
                 nmax_reserve=DEFAULT_NMAX_RESERVE, 
                 res_keep_prob=DEFAULT_RESOURCE_KEEP_PROBABILITY,
                 is_attacker=False, ue_specific_logger=None): # Added ue_specific_logger
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger # Main logger for global events
        self.ue_logger = ue_specific_logger # UE-specific logger instance
        self.mobility_model = mobility_model
        
        self.pos = initial_pos
        self.speed = initial_speed # This is initial speed, actual speed is in mobility_model state for this UE
        self.direction_rad = 0 # Initial direction, actual is in mobility_model state
        # self.mobility_model_type = 0 # Not used here, managed by MobilityModel
        
        self.prsvp = prsvp
        self.slrrc = 0 
        self.slrrc_max = DEFAULT_SLRRC_MAX 
        self.slrrc_min = DEFAULT_SLRRC_MIN
        self.nmax_reserve = nmax_reserve
        self.resource_keep_probability = res_keep_prob
        
        self.selected_resources = [] 
        self.sensed_resource_map = {} 
        self.data_queue = [] 
        self.is_attacker = is_attacker
        self.all_other_ues = {}
        self.sci_decode_failed = {} # 新增：追蹤每個發送者的 SCI 解碼失敗情況

        self.action = env.process(self.run())
        self.bsm_generation_interval = BSM_GENERATION_INTERVAL_MS 
        if not self.is_attacker: # Attackers typically don't generate BSMs in this context
            self.env.process(self.generate_bsm_periodically())
        self.transmitting_time = 0
        self.in_emergency_state = False
        self.emergency_times = 0

    def run(self):
        while True:
            if not self.selected_resources or self.slrrc == 0:
                yield self.env.process(self.sps_resource_selection_procedure())

            if self.selected_resources and self.data_queue:
                sci_content = self.generate_sci()
                # The first resource in the list is often considered the primary or reference for SCI
                # if self.env.now
                yield self.env.process(self.send_sci(sci_content, self.selected_resources[0])) 
                self.slrrc -= 1
                # print(self.selected_resources)
                for pssch_idx, pssch_resource in enumerate(self.selected_resources):
                    if pssch_idx == 0: # Skip the first one as it's used for SCI
                        continue
                    if not self.data_queue:
                        self.global_logger.log_event(Time_ms=self.env.now, EventType="PSSCH_TX_SKIP_NO_DATA", UE_ID=self.ue_id, Resource=str(pssch_resource), 
                                              Details=f"Data queue empty for PSSCH {pssch_idx+1}/{len(self.selected_resources)}")
                        break 
                    bsm_data = self.data_queue.pop(0)
                    # print(f"ue_id:{self.ue_id}, pssch_resource[0]:{pssch_resource[0]}, env.now:{self.env.now % 100}, ans: {pssch_resource[0] >= self.env.now % 100}")
                    # if pssch_resource[0] >= self.env.now % 100:
                    #     print(f"ue_id:{self.ue_id}, pssch_resource[0]+100:{pssch_resource[0]}, env.now:{self.env.now % 100}, time_out: {pssch_resource[0] - (self.env.now % 100)}")
                    #     yield self.env.timeout(pssch_resource[0] - (self.env.now % 100))
                    #     self.transmitting_time += (pssch_resource[0] - (self.env.now % 100))
                    if pssch_resource[0] < self.env.now % 100:

                        print(f"ue_id:{self.ue_id}, self.selected_resources:{self.selected_resources}, pssch_resource[0]:{pssch_resource[0]}, env.now:{self.env.now % 100}, time_out: {pssch_resource[0] - (self.env.now % 100)}")
                    while pssch_resource[0] != self.env.now % 100:
                        yield self.env.timeout(SLOT_DURATION_MS)
                        self.transmitting_time += SLOT_DURATION_MS
                    yield self.env.process(self.send_pssch(bsm_data, pssch_resource))
                
                if self.slrrc == 0:
                    if random.random() < 1-self.resource_keep_probability:
                        self.slrrc = random.randint(self.slrrc_min, self.slrrc_max) 
                        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_GROUP_RE_RESERVED", UE_ID=self.ue_id, Resource=str(self.selected_resources), 
                                                Details=f"Re-reserved for {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC reset to {self.slrrc}")
                        self.ue_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_GROUP_RE_RESERVED", UE_ID=self.ue_id, Resource=str(self.selected_resources), 
                                                Details=f"Re-reserved for {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC reset to {self.slrrc}")
                    else:
                        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_GROUP_RELEASED", UE_ID=self.ue_id, Resource=str(self.selected_resources), 
                                                Details=f"Released {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC reached 0")
                        self.ue_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_GROUP_RELEASED", UE_ID=self.ue_id, Resource=str(self.selected_resources), 
                                                Details=f"Released {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC reached 0")
                        for res in self.selected_resources:
                            time = self.env.now
                            self.resource_pool.release_reservation(self.ue_id, res, time)
                        self.selected_resources = [] 
                else:
                        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_GROUP_KEPT", UE_ID=self.ue_id, Resource=str(self.selected_resources), 
                                            Details=f"Kept {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC decremented to {self.slrrc}")       
                # This else block was for initial selection, now handled by sps_resource_selection_procedure setting slrrc
                # else: 
                #     self.slrrc = self.slrrc_max
                #     self.global_logger.log_event(self.env.now, "RESOURCE_GROUP_INITIALLY_SELECTED", self.ue_id, resource=str(self.selected_resources), 
                #                           details=f"Initially selected {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC set to {self.slrrc}")
            if self.transmitting_time > self.prsvp:
                print(f'{self.ue_id}, self.env.now: {self.env.now}, self.transmitting_time: {self.transmitting_time}')
            yield self.env.timeout(self.prsvp - self.transmitting_time) # Wait for next PRSRP or shorter if no resource
            self.transmitting_time = 0
            self.time_out = 0
            #yield self.env.timeout(self.prsvp - (self.selected_resources[1][0] - self.selected_resources[0][0]) if self.selected_resources else self.bsm_generation_interval / 2) # Wait for next PRSRP or shorter if no resource

    def generate_bsm_periodically(self):
        bsm_counter = 0
        while True:
            # if self.ue_id == 'NormalUE-0':
            #     print("==============================")
            #     print(self.data_queue)
            yield self.env.timeout(self.bsm_generation_interval)
            # Update current position and speed from mobility model before generating BSM
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]
            self.speed = current_mobility_state["speed_mps"]
            if not self.in_emergency_state and random.random() < EMERGENCY_BSM_PROBABILITY:
                self.in_emergency_state = True
                self.emergency_times = random.randint(EMERGENCY_BSM_DURATION_MIN, EMERGENCY_BSM_DURATION_MAX)
                self.global_logger.log_event(
                                    Time_ms=self.env.now,
                                    EventType="EMERGENCY_STATE_START",
                                    UE_ID=self.ue_id,
                                    Details=f"Emergency state triggered. Will last for {self.emergency_times} slots."
                                )
                if self.ue_logger:
                    self.ue_logger.log_event(
                        Time_ms=self.env.now,
                        EventType="EMERGENCY_STATE_START",
                        UE_ID=self.ue_id,
                        Details=f"Duration: {self.emergency_times*100/SLOT_DURATION_MS} ms"
                    )
                bsm_content = {
                "id": bsm_counter, 
                "ue_id": self.ue_id, 
                "timestamp": self.env.now, 
                "pos": self.pos, 
                "speed": self.speed,
                "is_emergency": self.in_emergency_state # 新增的欄位
                }
            elif self.in_emergency_state and self.emergency_times > 0:
                self.emergency_times -= 1
                self.global_logger.log_event(
                                    Time_ms=self.env.now,
                                    EventType="EMERGENCY_STATE_START",
                                    UE_ID=self.ue_id,
                                    Details=f"Emergency state triggered. Will last for {self.emergency_times} slots."
                                )
                if self.ue_logger:
                    self.ue_logger.log_event(
                        Time_ms=self.env.now,
                        EventType="EMERGENCY_STATE_START",
                        UE_ID=self.ue_id,
                        Details=f"Duration: {self.emergency_times*100/SLOT_DURATION_MS} ms"
                    )
                bsm_content = {
                "id": bsm_counter, 
                "ue_id": self.ue_id, 
                "timestamp": self.env.now, 
                "pos": self.pos, 
                "speed": self.speed,
                "is_emergency": self.in_emergency_state # 新增的欄位
                }
                if self.emergency_times == 0:
                    self.in_emergency_state = False
            else:
                bsm_content = {"id": bsm_counter, "ue_id": self.ue_id, "timestamp": self.env.now, "pos": self.pos, "speed": self.speed, "is_emergency": False}
                self.global_logger.log_event(Time_ms=self.env.now, EventType="BSM_GENERATED", UE_ID=self.ue_id, Content_Abstract=f"BSM_ID:{bsm_counter}")
            
            self.data_queue.append(bsm_content)
            bsm_counter += 1
            # if self.ue_id == 'NormalUE-0':
            #     print(self.data_queue)
            #     print("==============================")

    def _get_relevant_sensed_scis(self, current_time, sensing_window_start_time, rsrp_threshold):
        relevant_scis_info = [] # Store dicts with sci_content and other info
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="SPS_SENSING_MAP_CHECK", Details=f"Checking {len(self.sensed_resource_map)} entries for RSRP >= {rsrp_threshold:.2f} dBm in window [{sensing_window_start_time}, {current_time}]")
        
        for res_key, info in self.sensed_resource_map.items():
            # res_key is the PSSCH resource the SCI is announcing
            if info["timestamp"] >= sensing_window_start_time and info["timestamp"] <= current_time:
                if info["RSRP"] >= rsrp_threshold:
                    relevant_scis_info.append(info) # Append the whole info dict
                    if self.ue_logger:
                        self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="SPS_SENSED_SCI_FOR_EXCLUSION", 
                                                 Resource_Details=str(res_key), Related_UE_ID=info["sci_content"].get("sender_id"), 
                                                 SCI_Content_Abstract=str(info["sci_content"]),
                                                 RSRP_dBm=info["RSRP"], Details="Used for exclusion list")
        return [info["sci_content"] for info in relevant_scis_info] # Return only sci_content as before for compatibility

    def sps_resource_selection_procedure(self):
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SPS_START_SELECTION", UE_ID=self.ue_id)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now ,UE_ID=self.ue_id, EventType="SPS_SELECTION_PROCESS_START")

        current_time = self.env.now
        sensing_window_start_time = current_time - DEFAULT_SENSING_WINDOW_T0
        
        # Get all candidate resources from pool initially (step L0 in standard)
        all_pool_candidates = self.resource_pool.get_candidate_resources(self.ue_id, [], 1.0, current_time) # Get all initially available without sensing filter for L0

        # Dynamic RSRP Threshold Logic
        preliminary_relevant_sensed_scis_content = self._get_relevant_sensed_scis(current_time, sensing_window_start_time, DEFAULT_SENSING_RSRP_THRESHOLD_DBM)
        # Exclude resources based on these SCIs from all_pool_candidates to get num_available_resources_initial
        # This step is simplified here; a more accurate way would be to use resource_pool.get_candidate_resources
        # which internally handles exclusion based on sensed SCIs and its own reservations.
        # For now, we use the count from a direct call to get_candidate_resources for the X% logic.
        initial_candidate_list_for_X_percent = self.resource_pool.get_candidate_resources(self.ue_id, preliminary_relevant_sensed_scis_content, 1.0, current_time) # Get all candidates after sensing
        num_available_resources_initial = len(initial_candidate_list_for_X_percent)

        selection_window_duration_slots = DEFAULT_SELECTION_WINDOW_T2_SLOTS - DEFAULT_SELECTION_WINDOW_T1_SLOTS + 1
        total_resources_in_selection_window = NUM_SUBCHANNELS * selection_window_duration_slots
        
        effective_rsrp_threshold = DEFAULT_SENSING_RSRP_THRESHOLD_DBM
        if total_resources_in_selection_window > 0:
            percentage_available = num_available_resources_initial / total_resources_in_selection_window
            if percentage_available < DEFAULT_X_PERCENT_CANDIDATES_THRESHOLD:
                effective_rsrp_threshold = DEFAULT_SENSING_RSRP_THRESHOLD_DBM + 3.0
                self.global_logger.log_event(Time_ms=current_time, EventType="SPS_RSRP_THRESHOLD_ADJUSTED", UE_ID=self.ue_id, 
                                      Details=f"Initial available {num_available_resources_initial}/{total_resources_in_selection_window} ({percentage_available*100:.1f}%) < {DEFAULT_X_PERCENT_CANDIDATES_THRESHOLD*100}%. RSRP threshold increased to {effective_rsrp_threshold:.2f} dBm")
        
        self.global_logger.log_event(Time_ms=current_time, EventType="SPS_RSRP_THRESHOLD_SET", UE_ID=self.ue_id, Details=f"Effective RSRP Threshold: {effective_rsrp_threshold:.2f} dBm")
        if self.ue_logger:
             self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="SPS_EFFECTIVE_RSRP_SET", Details=f"RSRP_dBm: {effective_rsrp_threshold:.2f}")

        final_relevant_sensed_scis_content = self._get_relevant_sensed_scis(current_time, sensing_window_start_time, effective_rsrp_threshold)
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SPS_SENSING_COMPLETE", UE_ID=self.ue_id, Details=f"Found {len(final_relevant_sensed_scis_content)} relevant SCIs (RSRP >= {effective_rsrp_threshold:.2f}dBm)")
        
        # Get final candidate list from resource pool, which considers its own reservations and the sensed SCIs provided
        candidate_resources = self.resource_pool.get_candidate_resources(self.ue_id, final_relevant_sensed_scis_content, DEFAULT_X_PERCENT_CANDIDATES_FOR_POOL, current_time)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, EventType="SPS_FINAL_CANDIDATES_FROM_POOL", 
                                     Raw_Candidate_Resources=str(candidate_resources), Details=f"Count: {len(candidate_resources)} after RSRP {effective_rsrp_threshold:.2f}dBm and pool exclusion")
        
        if not candidate_resources or len(candidate_resources) < NUM_PSSCH_PER_SCI:
            self.global_logger.log_event(Time_ms=self.env.now, EventType="SPS_NO_CANDIDATES_FOUND", UE_ID=self.ue_id, 
                                  Details=f"Not enough candidates ({len(candidate_resources)}) for {NUM_PSSCH_PER_SCI} PSSCH(s). Retrying after backoff.")
            if self.ue_logger:
                self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SPS_SELECTION_FAILED_NO_CANDIDATES", Raw_Candidate_Resources=str(candidate_resources), Details=f"Needed {NUM_PSSCH_PER_SCI}")
            self.selected_resources = []
            yield self.env.timeout(random.uniform(self.prsvp * 0.1, self.prsvp * 0.25)) # Shorter backoff
            return

        self.selected_resources = []
        if len(candidate_resources) >= NUM_PSSCH_PER_SCI:
            # 先將資源依照 slot index % 100 分組
            slot_groups = {}
            for res in candidate_resources:
                sec = res[0] % 100
                if sec not in slot_groups:
                    slot_groups[sec] = []
                slot_groups[sec].append(res)
            # 只選不同秒的資源
            if len(slot_groups) >= NUM_PSSCH_PER_SCI:
                chosen_secs = random.sample(list(slot_groups.keys()), NUM_PSSCH_PER_SCI)
                self.selected_resources = [random.choice(slot_groups[sec]) for sec in chosen_secs]
        # else: # This case should be caught by the check above, but as a fallback:
            # self.selected_resources = candidate_resources # Take what's available if less than needed

        if not self.selected_resources:
            self.global_logger.log_event(Time_ms=self.env.now, EventType="SPS_SELECTION_FAILED_POST_SAMPLE", UE_ID=self.ue_id)
            if self.ue_logger:
                 self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SPS_SELECTION_FAILED_POST_SAMPLE")
            yield self.env.timeout(random.uniform(self.prsvp * 0.1, self.prsvp * 0.25))
            return
        self.selected_resources.sort()
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SPS_RESOURCES_SELECTED", UE_ID=self.ue_id, Resource=str(self.selected_resources), 
                              Details=f"Selected {len(self.selected_resources)} PSSCH(s) from {len(candidate_resources)} candidates")
        if self.ue_logger:
            for res in self.selected_resources:
                self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SPS_RESOURCE_SELECTED", 
                                         Resource_Details=str(res), Is_Selected_By_Self=True, 
                                         Details=f"Part of group: {self.selected_resources}")
        resource_trigger = self.selected_resources[0][0]
        for res_to_reserve in self.selected_resources:
            # Reservation in resource pool is for one PRSRP period initially, re-selection or re-reservation handles subsequent ones.
            self.resource_pool.reserve_resource(self.ue_id, res_to_reserve, self.prsvp, 1, self.env.now) # Reserve for 1*PRSRP
        self.selected_resources = [(res[0] % 100, res[1] % 100) for res in self.selected_resources]
        self.slrrc = random.randint(self.slrrc_min,self.slrrc_max) # Reset SLRRC for the new set of resources
        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_GROUP_INITIALLY_SELECTED", UE_ID=self.ue_id, Resource=str(self.selected_resources),SLRRC = self.slrrc, 
                                          Details=f"Initially selected {NUM_PSSCH_PER_SCI} PSSCH(s), SLRRC set to {self.slrrc}")
        yield self.env.timeout(resource_trigger - self.env.now)

    def generate_sci(self):
        sci_content = {
            "sender_id": self.ue_id,
            "prsvp": self.prsvp,
            "nsci": NUM_PSSCH_PER_SCI,
            "timestamp": self.env.now,
            "pssch_resources": self.selected_resources # Always include the list of PSSCH resources
        }
        # For backward compatibility or specific single-resource SCI logging, can add this:
        # if NUM_PSSCH_PER_SCI == 1 and self.selected_resources:
        #     sci_content["pssch_resource"] = self.selected_resources[0]
        
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCI_GENERATED", UE_ID=self.ue_id, Content_Abstract=str(sci_content))
        return sci_content

    def send_sci(self, sci_content, reference_pssch_resource): # reference_pssch_resource is the first in the list
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCI_TX_START", UE_ID=self.ue_id, Resource=f"CTRL_FOR_{reference_pssch_resource}", 
                              Content_Abstract=str(sci_content), Power_dBm=self.comm_model.default_tx_power_sci)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SCI_TX", 
                                     Resource_Details=f"CTRL_FOR_{reference_pssch_resource}", SLRRC = self.slrrc,
                                     SCI_Content_Abstract=str(sci_content), Is_Transmission=True)

        self.resource_pool.log_transmission(self.ue_id, reference_pssch_resource, "SCI", sci_content, 
                                            self.comm_model.default_tx_power_sci, self.comm_model.sci_tx_time, is_control=True)
        yield self.env.process(self.send_pssch(self.data_queue[0],resource = reference_pssch_resource, is_first_pssch=True)) # Send the first data in the queue on the reference resource
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCI_TX_END", UE_ID=self.ue_id, Resource=f"CTRL_FOR_{reference_pssch_resource}")
        # yield self.env.timeout(SLOT_DURATION_MS*(self.selected_resources[1][0] - reference_pssch_resource[0])) # Wait for the end of the slot before sending PSSCH
        self.transmitting_time += SLOT_DURATION_MS
        yield self.env.timeout(SLOT_DURATION_MS)


    def send_pssch(self, data, resource, is_first_pssch=False):
        pssch_data = data.copy()
        tx_time = SLOT_DURATION_MS
        if pssch_data.get("is_emergency") == True and is_first_pssch == True:
            pssch_data["is_emergency"] = False
        self.global_logger.log_event(
            Time_ms=self.env.now, 
            EventType="PSSCH_TX_START", 
            UE_ID=self.ue_id, 
            Resource=str(resource), 
            Content_Abstract=f"DataID:{pssch_data}",
            Power_dBm=self.comm_model.default_tx_power_pssch
        )
        if self.ue_logger:
            self.ue_logger.log_event(
                Time_ms=self.env.now, 
                UE_ID=self.ue_id, 
                EventType="PSSCH_TX", 
                Resource_Details=str(resource), 
                PSSCH_Content_Abstract=f"DataID:{pssch_data}",
                Is_Transmission=True
            )

        self.resource_pool.log_transmission(
            self.ue_id, 
            resource, 
            "PSSCH", 
            pssch_data, 
            self.comm_model.default_tx_power_pssch, 
            tx_time
        )
        self.global_logger.log_event(
            Time_ms=self.env.now, 
            EventType="PSSCH_TX_END", 
            UE_ID=self.ue_id, 
            Resource=str(resource)
        )
        if not is_first_pssch:
            self.transmitting_time += SLOT_DURATION_MS
            yield self.env.timeout(SLOT_DURATION_MS)

    def receiver_process(self):
        while True:
            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
            current_time = self.env.now
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]
            self.speed = current_mobility_state["speed_mps"]
            
            active_transmissions_copy = dict(self.resource_pool.active_transmissions)

            for trans_key, trans_info in active_transmissions_copy.items():
                if not (trans_info["start_time"] <= current_time <= trans_info["end_time"]):
                    continue

                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id: continue

                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]
                signal_type = trans_info["type"]
                content = trans_info["content"]
                
                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object: continue
                sender_pos = sender_ue_object.pos # Get current pos of sender

                distance = self.comm_model.calculate_distance(self.pos, sender_pos)
                if distance > MAX_COMM_RANGE_M: continue 
                
                desired_signal_rx_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if desired_signal_rx_power_dbm < MIN_RX_POWER_DBM: continue

                interference_power_linear = 0.0
                # Calculate interference from other UEs
                for other_trans_key, other_trans_info in active_transmissions_copy.items():
                    if other_trans_key[0] == trans_key[0]: continue # Skip the desired signal itself
                    if not (other_trans_info["start_time"] <= current_time < other_trans_info["end_time"]):
                        continue
                    interferer_id = other_trans_info["ue_id"]
                    if interferer_id == self.ue_id: continue # Skip self-interference
                    
                    # Check if the interfering signal is on the same resource (time-frequency)
                    # This simplified model assumes SCI control part and PSSCH data part might interfere if on same "resource" tuple
                    if other_trans_info["resource"] != resource: 
                        # More granular check: if SCI is for this PSSCH, or PSSCH for this SCI, they might not be same exact resource block
                        # For now, assume direct resource tuple match means interference.
                        continue

                    interferer_ue_object = self.all_other_ues.get(interferer_id)
                    if not interferer_ue_object: continue
                    interferer_pos = interferer_ue_object.pos
                    dist_to_interferer = self.comm_model.calculate_distance(self.pos, interferer_pos)
                    if dist_to_interferer > MAX_COMM_RANGE_M: continue
                    rx_power_from_interferer_dbm = self.comm_model.calculate_received_power_dbm(other_trans_info["tx_power"], dist_to_interferer)
                    if rx_power_from_interferer_dbm > MIN_RX_POWER_DBM:
                        interference_power_linear += self.comm_model.dbm_to_linear(rx_power_from_interferer_dbm)
                
                # Calculate interference from active jamming
                active_jamming_copy = dict(self.resource_pool.active_jamming)
                for jam_key, jam_info in active_jamming_copy.items():
                    if not (jam_info["start_time"] <= current_time < jam_info["end_time"]):
                        continue
                    if jam_info["resource"] == resource:
                        jammer_id = jam_info["ue_id"]
                        jammer_ue_object = self.all_other_ues.get(jammer_id) 
                        if not jammer_ue_object: continue
                        jammer_pos = jammer_ue_object.pos
                        dist_to_jammer = self.comm_model.calculate_distance(self.pos, jammer_pos)
                        if dist_to_jammer > MAX_COMM_RANGE_M: continue
                        rx_power_from_jammer_dbm = self.comm_model.calculate_received_power_dbm(jam_info["power"], dist_to_jammer)
                        if rx_power_from_jammer_dbm > MIN_RX_POWER_DBM:
                            interference_power_linear += self.comm_model.dbm_to_linear(rx_power_from_jammer_dbm)

                sinr_db = self.comm_model.calculate_sinr_db(desired_signal_rx_power_dbm, interference_power_linear, self.comm_model.noise_power_linear)
                #print(f"UE:{self.ue_id}, signal_type:{signal_type}, sender:{sender_id}, distance:{distance:.1f}m, SINR:{sinr_db:.2f}dB, tx_power:{tx_power_dbm:.2f}dBm, interference_power_linear:{interference_power_linear:.2f}W")
                channel = trans_key[3]
                if channel == "PSCCH":
                    self.decode_sci(content, sinr_db, sender_id, distance, desired_signal_rx_power_dbm, resource) 
                elif channel == "PSSCH":
                    self.decode_pssch(content, sinr_db, sender_id, distance, desired_signal_rx_power_dbm, resource)

    def decode_sci(self, sci_content, sinr, sender_id, distance, rx_power_dbm, pssch_resource_ref):
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SCI_RX_ATTEMPT", 
                                     Resource_Details=str(pssch_resource_ref), Related_UE_ID=sender_id, 
                                     SCI_Content_Abstract=str(sci_content), RSRP_dBm=rx_power_dbm, SINR_dB=sinr)

        is_success = self.comm_model.is_decoded_successfully(sinr, "SCI")
        outcome = "SUCCESS" if is_success else "FAILURE"
        log_details = f"Sender: {sender_id}, RxPower: {rx_power_dbm:.2f}dBm, SINR: {sinr:.2f}dB, Dist: {distance:.1f}m"
        event_type_global = "SCI_DECODE_SUCCESS" if is_success else "SCI_DECODE_FAIL"
        self.global_logger.log_event(Time_ms=self.env.now, EventType=event_type_global, UE_ID=self.ue_id, Target_UE_ID=sender_id, Resource=str(pssch_resource_ref), 
                              SignalType="SCI", Content_Abstract=str(sci_content), Power_dBm=rx_power_dbm, SINR_dB=sinr, Distance_m=distance, 
                              TransmissionOutcome=outcome, Details=log_details)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SCI_RX_OUTCOME", 
                                     Resource_Details=str(pssch_resource_ref), Related_UE_ID=sender_id, Outcome=outcome,
                                     SCI_Content_Abstract=str(sci_content), RSRP_dBm=rx_power_dbm, SINR_dB=sinr)
        
        # 更新 SCI 解碼失敗狀態
        if not is_success:
            self.sci_decode_failed[sender_id] = True
        else:
            self.sci_decode_failed[sender_id] = False
            
        if is_success:
            announced_pssch_resources = sci_content.get("pssch_resources", [])
            # Fallback for older SCI format if needed, though current generate_sci always uses pssch_resources list
            # if not announced_pssch_resources and "pssch_resource" in sci_content:
            #     announced_pssch_resources = [sci_content["pssch_resource"]]
            
            for pssch_res in announced_pssch_resources:
                if pssch_res: # Ensure resource is not None or empty
                    self.sensed_resource_map[pssch_res] = {
                        "ue_id": sender_id, 
                        "RSRP": rx_power_dbm, 
                        "timestamp": self.env.now,
                        "sci_content": sci_content # Store the full SCI content that announced this
                    }
                    self.global_logger.log_event(Time_ms=self.env.now, EventType="SENSING_MAP_UPDATE_SCI", UE_ID=self.ue_id, Resource=str(pssch_res), 
                                          Details=f"PSSCH res {pssch_res} from UE {sender_id} added/updated from SCI.")
                    if self.ue_logger:
                        self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SENSING_MAP_UPDATE_FROM_SCI",
                                                 Resource_Details=str(pssch_res), Related_UE_ID=sender_id, RSRP_dBm=rx_power_dbm,
                                                 SCI_Content_Abstract=str(sci_content))

    def decode_pssch(self, pssch_content, sinr, sender_id, distance, rx_power_dbm, resource):
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="PSSCH_RX_ATTEMPT", 
                                     Resource_Details=str(resource), Related_UE_ID=sender_id, 
                                     PSSCH_Content_Abstract=f"BSM_ID:{pssch_content.get('id')}", RSRP_dBm=rx_power_dbm, SINR_dB=sinr)

        # 檢查發送者的 SCI 是否解碼失敗
        if self.sci_decode_failed.get(sender_id, False):
            is_success = False
            outcome = "FAILURE"
            log_details = f"PSSCH decode failed due to previous SCI decode failure. Sender: {sender_id}, RxPower: {rx_power_dbm:.2f}dBm, SINR: {sinr:.2f}dB, Dist: {distance:.1f}m. BSM_ID: {pssch_content.get('id', 'N/A')}"
        else:
            is_success = self.comm_model.is_decoded_successfully(sinr, "PSSCH")
            outcome = "SUCCESS" if is_success else "FAILURE"
            log_details = f"Sender: {sender_id}, RxPower: {rx_power_dbm:.2f}dBm, SINR: {sinr:.2f}dB, Dist: {distance:.1f}m. BSM_ID: {pssch_content.get('id', 'N/A')}"

        event_type_global = "PSSCH_DECODE_SUCCESS" if is_success else "PSSCH_DECODE_FAIL"
        self.global_logger.log_event(Time_ms=self.env.now, EventType=event_type_global, UE_ID=self.ue_id, Target_UE_ID=sender_id, Resource=str(resource), 
                              SignalType="PSSCH", Content_Abstract=f"BSM_ID:{pssch_content.get('id')}", Power_dBm=rx_power_dbm, SINR_dB=sinr, Distance_m=distance,
                              TransmissionOutcome=outcome, Details=log_details)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="PSSCH_RX_OUTCOME", 
                                     Resource_Details=str(resource), Related_UE_ID=sender_id, Outcome=outcome,
                                     PSSCH_Content_Abstract=f"BSM_ID:{pssch_content.get('id')}", RSRP_dBm=rx_power_dbm, SINR_dB=sinr)
        if is_success:
            self.global_logger.log_event(Time_ms=self.env.now, EventType="BSM_RECEIVED", UE_ID=self.ue_id, Target_UE_ID=sender_id, Content_Abstract=str(pssch_content))
            # Further BSM processing could happen here


class AttackerUE(VehicleUE):
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
                 initial_pos=(0,0), initial_speed=0, victim_ue_id=None, 
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM, jamming_duration_ms=ATTACKER_JAMMING_DURATION_MS,
                 ue_specific_logger=None): # Added ue_specific_logger
        super().__init__(env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
                         initial_pos, initial_speed, is_attacker=True, ue_specific_logger=ue_specific_logger)
        self.victim_ue_id = victim_ue_id
        self.jamming_power_dbm = jamming_power_dbm
        self.jamming_duration_ms = jamming_duration_ms
        self.victim_sci_info = None
        self.victim_target_resources = [] 
        self.victim_prsvp = None
        self.victim_last_sci_tx_time = -1
        self.victim_transmissions_on_resource_count = 0
        self.max_transmissions_to_attack_on_resource = ATTACKER_MAX_TRANSMISSIONS_ON_RESOURCE

        self.global_logger.log_event(Time_ms=self.env.now, EventType="ATTACKER_INIT", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id, 
                              Details=f"Jamming Power: {self.jamming_power_dbm}dBm. Max victim tx count: {self.max_transmissions_to_attack_on_resource}")
        # Attacker uses its own run logic, overriding the one from VehicleUE
        self.action = env.process(self.attacker_run_logic()) 
        self.target_change = False

    def attacker_run_logic(self):
        self.global_logger.log_event(Time_ms=self.env.now, EventType="ATTACKER_LOGIC_START", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id)
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="ATTACKER_PROCESS_START", Related_UE_ID=self.victim_ue_id)
        
        predicted_victim_tx_start_time = -1
        while True:
            current_attacker_time = self.env.now
            self.check_sensed_scis_for_victim() # Uses the sensed_resource_map populated by inherited receiver_process

            if self.victim_sci_info:
                new_victim_resources = self.victim_sci_info["resources"][0]
                new_victim_prsvp = self.victim_sci_info["prsvp"]
                new_victim_sci_time = self.victim_sci_info["sci_timestamp"]
                if set(new_victim_resources) != set(self.victim_target_resources):
                    self.target_change = True

                if set(new_victim_resources) != set(self.victim_target_resources) or new_victim_prsvp != self.victim_prsvp:
                    self.global_logger.log_event(Time_ms=current_attacker_time, EventType="ATTACKER_VICTIM_NEW_SCI_DETECTED", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id,
                                          Resource=str(new_victim_resources), Details=f"Victim changed. Old res: {self.victim_target_resources}, New PRSRP: {new_victim_prsvp}")
                    if self.ue_logger:
                        self.ue_logger.log_event(Time_ms=current_attacker_time, UE_ID=self.ue_id, EventType="ATTACKER_TARGET_UPDATE", 
                                                 Related_UE_ID=self.victim_ue_id, Resource_Details=str(new_victim_resources),
                                                 SCI_Content_Abstract=str(self.victim_sci_info.get("full_sci_content")))
                    self.victim_target_resources = new_victim_resources
                    self.victim_prsvp = new_victim_prsvp
                    self.victim_last_sci_tx_time = new_victim_sci_time
                    self.victim_transmissions_on_resource_count = 0
                    self.transmitting_time += 1
                elif new_victim_sci_time > self.victim_last_sci_tx_time: # Victim re-announced same resource
                    self.victim_last_sci_tx_time = new_victim_sci_time # Update to latest SCI time for this resource set
                    self.global_logger.log_event(Time_ms=current_attacker_time, EventType="ATTACKER_VICTIM_SCI_REFRESH", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id, Resource=str(self.victim_target_resources))
                    # No change to victim_transmissions_on_resource_count here, as it counts PSSCHs for the *current* resource set
            if self.victim_target_resources and self.victim_prsvp is not None and self.victim_last_sci_tx_time >= 0:
                if self.victim_transmissions_on_resource_count < self.max_transmissions_to_attack_on_resource and self.target_change == False:
                    # Predict based on the *start* of the victim's SCI transmission plus subsequent PRSVPs
                    # Victim's PSSCH starts after its SCI_TX_TIME
                    predicted_victim_tx_start_time = self.victim_last_sci_tx_time + self.comm_model.sci_tx_time + (self.victim_transmissions_on_resource_count * self.victim_prsvp)
                    time_to_predicted_tx = predicted_victim_tx_start_time - current_attacker_time

                    # if 0 < time_to_predicted_tx < self.victim_prsvp * 1.1: 
                    # print("current_time:", current_attacker_time)
                    # print("Victim target resources:", self.victim_target_resources)
                    if current_attacker_time % 100 == self.victim_target_resources[0]: 
                        self.global_logger.log_event(Time_ms=current_attacker_time, EventType="ATTACKER_PLANNING_JAM", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id,
                                              Resource=str(self.victim_target_resources), 
                                              Details=f"Predict victim PSSCH tx at {predicted_victim_tx_start_time:.2f} (in {time_to_predicted_tx:.2f}ms). Victim tx count on res: {self.victim_transmissions_on_resource_count}")
                        # yield self.env.timeout(time_to_predicted_tx)
                        
                        jam_time_now = self.env.now
                        # for res_to_jam in self.victim_target_resources:
                        #     self.global_logger.log_event(Time_ms=jam_time_now, EventType="ATTACKER_JAM_START", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id, 
                        #                           Resource=str(res_to_jam), Power_dBm=self.jamming_power_dbm,
                        #                           Details=f"Jamming victim PSSCH. Victim tx count: {self.victim_transmissions_on_resource_count}")
                        #     if self.ue_logger:
                        #         self.ue_logger.log_event(Time_ms=jam_time_now, UE_ID=self.ue_id, EventType="ATTACKER_JAM_EXECUTE", 
                        #                                  Resource_Details=str(res_to_jam), Related_UE_ID=self.victim_ue_id, 
                        #                                  Details=f"Power: {self.jamming_power_dbm}dBm")
                        #     self.resource_pool.log_jamming(self.ue_id, res_to_jam, self.jamming_power_dbm, self.jamming_duration_ms)
                        # Attacker is busy for its jamming_duration_ms, but log_jamming handles the period.
                        # The timeout for the attacker's main loop will handle next decision.
                        # Increment count after this PSSCH attempt cycle (done after the main if block)
                        self.global_logger.log_event(Time_ms=jam_time_now, EventType="ATTACKER_JAM_START", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id, 
                                                Resource=str(self.victim_target_resources[0]), Power_dBm=self.jamming_power_dbm,
                                                Details=f"Jamming victim PSSCH. Victim tx count: {self.victim_transmissions_on_resource_count}")
                        
                        if self.ue_logger:
                            self.ue_logger.log_event(Time_ms=jam_time_now, UE_ID=self.ue_id, EventType="ATTACKER_JAM_EXECUTE", 
                                                        Resource_Details=str(self.victim_target_resources[0]), Related_UE_ID=self.victim_ue_id, 
                                                        Details=f"Power: {self.jamming_power_dbm}dBm")
                        self.resource_pool.log_jamming(self.ue_id, self.victim_target_resources, self.jamming_power_dbm, self.jamming_duration_ms)
                    elif time_to_predicted_tx <= 0: 
                        self.global_logger.log_event(Time_ms=current_attacker_time, EventType="ATTACKER_PREDICTED_TX_MISSED_OR_NOW", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id,
                                              Resource=str(self.victim_target_resources), Details=f"Predicted time {predicted_victim_tx_start_time:.2f} was in past/now. Incrementing victim count for next cycle.")
                elif self.target_change: 
                    self.global_logger.log_event(Time_ms=current_attacker_time, EventType="ATTACKER_MAX_TX_ON_RESOURCE_REACHED", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id,
                                          Resource=str(self.victim_target_resources), Details=f"Reached {self.max_transmissions_to_attack_on_resource} attacks. Reverting to listening.")
                    if self.ue_logger and self.target_change == False:
                        self.ue_logger.log_event(Time_ms=current_attacker_time, UE_ID=self.ue_id, EventType="ATTACKER_TARGET_RESET", 
                                                 Related_UE_ID=self.victim_ue_id, Resource_Details=str(self.victim_target_resources),
                                                 Details="Max attacks reached for this resource set.")                 
                        self.victim_target_resources = []
                        self.victim_prsvp = None
                        self.victim_last_sci_tx_time = -1
                        self.victim_transmissions_on_resource_count = 0 # Reset count for new target acquisition
                    elif self.target_change == True:
                        self.global_logger.log_event(Time_ms=current_attacker_time, EventType="ATTACKER_TARGET_RESET", UE_ID=self.ue_id, Target_UE_ID=self.victim_ue_id,
                                              Resource=str(self.victim_target_resources), Details="Victim changed resources.")
                        if self.ue_logger:
                            self.ue_logger.log_event(Time_ms=current_attacker_time, UE_ID=self.ue_id, EventType="ATTACKER_TARGET_RESET", 
                                                     Related_UE_ID=self.victim_ue_id, Resource_Details=str(self.victim_target_resources),
                                                     Details="Victim changed resources.")
            
            # Increment count if an attack cycle was relevant (i.e., victim was targeted and predicted PSSCH time has passed or is now)
            if self.victim_target_resources and self.victim_last_sci_tx_time >=0 and current_attacker_time >= predicted_victim_tx_start_time and predicted_victim_tx_start_time > 0:
                 self.victim_transmissions_on_resource_count +=1
                 predicted_victim_tx_start_time = -1 # Reset to avoid re-incrementing in the same polling cycle

            # Attacker's own polling interval, independent of victim's PRSRP for decision making
            # print(max(RECEIVER_POLLING_INTERVAL_MS, self.prsvp / 10 if self.victim_prsvp else 10))
            # yield self.env.timeout(max(RECEIVER_POLLING_INTERVAL_MS, self.prsvp / 10 if self.victim_prsvp else 10))
            # print(self.prsvp - 1 - self.transmitting_time if self.victim_prsvp else RECEIVER_POLLING_INTERVAL_MS)
            # yield self.env.timeout(self.prsvp  - self.transmitting_time if self.victim_prsvp and self.victim_transmissions_on_resource_count <= 5 else RECEIVER_POLLING_INTERVAL_MS)
            # self.transmitting_time = 0
            yield self.env.timeout(SLOT_DURATION_MS)
            self.target_change = False

    def check_sensed_scis_for_victim(self):
        latest_timestamp = -1
        if not self.victim_ue_id: return None
        latest_victim_sci_info = None
        # Iterate over a copy if modification during iteration is a concern, though here it's read-only
        for res_key, sensed_info in self.sensed_resource_map.items():
            sci_content = sensed_info.get("sci_content")
            if sci_content and sci_content.get("sender_id") == self.victim_ue_id:
                # print("Victim SCI found:", sci_content)
                sci_timestamp = sci_content.get("timestamp", -1)
                if sci_timestamp > latest_timestamp:
                    latest_timestamp = sci_timestamp
                    announced_pssch_list = sci_content.get("pssch_resources", [])
                    # Fallback for older SCI format if needed
                    # if not announced_pssch_list and "pssch_resource" in sci_content: 
                    #     announced_pssch_list = [sci_content["pssch_resource"]]
                    
                    if announced_pssch_list: # Ensure there are resources announced
                        latest_victim_sci_info = {
                            "resources": announced_pssch_list, 
                            "prsvp": sci_content.get("prsvp"),
                            "sci_timestamp": sci_timestamp,
                            "full_sci_content": sci_content # For logging by attacker if needed
                        }
        if latest_victim_sci_info is not None:
            self.victim_sci_info = latest_victim_sci_info
            # return latest_victim_sci_info

    # Attacker does not generate BSMs or select SPS resources for its own transmission in this model
    def generate_bsm_periodically(self):
        # Override to do nothing or log if needed
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="ATTACKER_BSM_GEN_SKIP")
        yield self.env.timeout(SIM_DURATION_MS * 2) # Effectively disable by waiting longer than sim


    def sps_resource_selection_procedure(self):
        # Override to do nothing or log if needed
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="ATTACKER_SPS_SKIP")
        yield self.env.timeout(SIM_DURATION_MS * 2) # Effectively disable


