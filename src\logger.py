# logger.py
import csv
import os

class Logger:
    def __init__(self, filename="simulation_log.csv", log_dir="logs", fieldnames=None):
        # Determine the directory of the current script (logger.py)
        # This is useful if log_dir is meant to be relative to logger.py's location,
        # but typically log_dir will be controlled by the main simulator script.
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # If log_dir is a relative path, it's often interpreted relative to the CWD (Current Working Directory)
        # or can be made relative to the script_dir if that's the desired behavior.
        # For this project, simulator.py will manage and create the log directories.
        actual_log_dir = log_dir

        # Ensure the log directory exists (simulator.py should handle this, but good for robustness)
        if not os.path.exists(actual_log_dir):
            try:
                os.makedirs(actual_log_dir)
                print(f"Logger created directory: {actual_log_dir}")
            except OSError as e:
                print(f"CRITICAL ERROR: Could not create log directory {actual_log_dir}: {e}")
                # Decide on error handling: raise, or log to a default location, or disable logging for this instance
                self.log_filepath = None # Indicate logging is not possible
                self.writer = None
                return
                
        self.log_filepath = os.path.join(actual_log_dir, filename)
        
        if fieldnames:
            self.fieldnames = fieldnames
        else:
            self.fieldnames = [
                "Time_ms", "EventType", "UE_ID", 
                "Target_UE_ID", "Resource","SLRRC", "SignalType", 
                "Content_Abstract", "Power_dBm", "SINR_dB", 
                "Distance_m", "Position", "TransmissionOutcome", "Details"
            ]
        
        # Initialize with header
        try:
            with open(self.log_filepath, "w", newline="") as csvfile:
                self.writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
                self.writer.writeheader()
            print(f"Logger initialized. Logging to: {self.log_filepath}")
        except Exception as e:
            print(f"CRITICAL ERROR: Could not initialize log file {self.log_filepath}: {e}")
            self.log_filepath = None # Indicate logging is not possible
            self.writer = None

    def log_event(self, **kwargs):
        """Logs a generic event to the CSV file using provided kwargs matching fieldnames."""
        if not self.log_filepath or not self.writer:
            # print(f"Logging disabled or logger not properly initialized for {self.log_filepath}")
            return

        # Ensure Time_ms is formatted if present
        if "Time_ms" in kwargs and isinstance(kwargs["Time_ms"], (float, int)):
            kwargs["Time_ms"] = f"{kwargs['Time_ms']:.2f}"
        
        # Fill missing fields with "N/A" to match the header
        log_entry = {field: kwargs.get(field, "N/A") for field in self.fieldnames}
        
        try:
            with open(self.log_filepath, "a", newline="") as csvfile:
                # Re-create DictWriter for append mode to ensure header consistency if file was empty after init fail
                writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
                # writer.writerow(log_entry) # This was the original line
                # Check if file is empty to write header (in case of 'a' mode on a new/cleared file)
                # This is a bit redundant if __init__ always succeeds in writing header to a new file.
                # However, if the file was somehow created but header writing failed, this might be useful.
                # For simplicity, assuming __init__ handles header for new files.
                csvfile.seek(0, os.SEEK_END) # Go to the end of file
                if csvfile.tell() == 0: # Check if file is empty
                    writer.writeheader() # Write header if file is empty
                writer.writerow(log_entry)

        except Exception as e:
            print(f"Error writing to log file {self.log_filepath}: {e}")

    def close(self):
        if self.log_filepath:
            print(f"Logging finished for {self.log_filepath}")
        # No specific file handle to close here as it's opened/closed per write

# Example Usage (for testing this file independently)
if __name__ == "__main__":
    base_test_log_dir = "./temp_logs_logger_test"
    
    # Test global logger
    global_log_dir = os.path.join(base_test_log_dir, "global")
    logger_global = Logger(filename="test_global_log.csv", log_dir=global_log_dir)
    if logger_global.log_filepath:
        logger_global.log_event(Time_ms=100.50, EventType="BSM_GENERATED", UE_ID="UE1", Details="BSM ID 1")
        logger_global.log_event(Time_ms=103.00, EventType="PSSCH_RX_OUTCOME", UE_ID="UE2", Target_UE_ID="UE1", 
                           Resource="(slot10, subch2)", SignalType="PSSCH", SINR_dB=15.5, 
                           TransmissionOutcome="SUCCESS", Details="BSM decoded")
        logger_global.close()
        print(f"Test global log created at {logger_global.log_filepath}")

    # Test UE-specific logger
    ue_specific_log_dir = os.path.join(base_test_log_dir, "ue_specific")
    ue_fieldnames = [
        "Time_ms", "EventType", "Resource_Details", "SCI_Content_Abstract", 
        "PSSCH_Content_Abstract", "RSRP_dBm", "SINR_dB", "Is_Selected_By_Self", 
        "Is_Transmission", "Related_UE_ID", "Outcome", "Raw_Candidate_Resources", "Details"
    ]
    logger_ue1 = Logger(filename="UE1_test.csv", log_dir=ue_specific_log_dir, fieldnames=ue_fieldnames)
    if logger_ue1.log_filepath:
        logger_ue1.log_event(Time_ms=100.50, EventType="SPS_RESOURCE_SELECTED", UE_ID="UE1", Resource_Details="(10,1)", Is_Selected_By_Self=True)
        logger_ue1.log_event(Time_ms=101.00, EventType="SCI_TX", UE_ID="UE1", SCI_Content_Abstract="SCI for (10,1)", Is_Transmission=True)
        logger_ue1.log_event(Time_ms=102.00, EventType="SCI_RX_OUTCOME", UE_ID="UE1", Related_UE_ID="UE2", SCI_Content_Abstract="SCI from UE2", Outcome="SUCCESS", RSRP_dBm=-80.0)
        logger_ue1.close()
        print(f"Test UE-specific log created at {logger_ue1.log_filepath}")

