# mobility.py
import math
import random
from src.CONFIG import *

class MobilityModel:
    def __init__(self, logger, sim_env, ues_dict, area_dimensions=(1000, 1000)):
        self.logger = logger
        self.env = sim_env
        self.ues_dict = ues_dict
        self.area_width = area_dimensions[0]
        self.area_height = area_dimensions[1]
        print(f"MobilityModel initialized for area {self.area_width}x{self.area_height}.")

    def initialize_ue_state(self, ue_id, model_type=MODEL_LINEAR, initial_pos=None, initial_speed_mps=10.0, initial_direction_deg=0.0):
        ue = self.ues_dict.get(ue_id)
        if not ue:
            print(f"Error: UE {ue_id} not found in MobilityModel for initialization.")
            return
        ue.mobility_model_type = model_type
        if initial_pos:
            ue.pos = initial_pos
        else:
            ue.pos = (random.uniform(0, self.area_width), random.uniform(0, self.area_height))
        ue.speed_mps = initial_speed_mps
        ue.direction_rad = math.radians(initial_direction_deg)
        if model_type == MODEL_RANDOM_WAYPOINT:
            ue.current_waypoint = self._generate_random_waypoint()
            ue.min_speed_mps = 5
            ue.max_speed_mps = 15
            ue.pause_time_s = random.uniform(0, 5)
            ue.is_paused = False
        self.logger.log_event(Time_ms=self.env.now, EventType="MOBILITY_INIT", UE_ID=ue_id, 
                              Details=f"Model: {model_type}, Pos: {ue.pos}, Speed: {ue.speed_mps:.2f} m/s, Dir: {initial_direction_deg:.2f} deg")
        print(f"Time {self.env.now}: Mobility for UE {ue_id} initialized. Pos: {ue.pos}, Speed: {ue.speed_mps:.2f} m/s, Dir: {initial_direction_deg:.2f} deg")

    def get_ue_state(self, ue_id):
        """Returns the current mobility state (pos, speed) of a UE."""
        ue = self.ues_dict.get(ue_id)
        if ue:
            return {"pos": ue.pos, "speed_mps": ue.speed_mps, "direction_rad": ue.direction_rad}
        else:
            # Fallback or error handling if UE not found, though it should always be in ues_dict
            print(f"Warning: UE {ue_id} not found in get_ue_state. Returning default state.")
            return {"pos": (0,0), "speed_mps": 0, "direction_rad": 0}

    def _generate_random_waypoint(self):
        return (random.uniform(0, self.area_width), random.uniform(0, self.area_height))

    def update_ue_position(self, ue_id, delta_time_s):
        ue = self.ues_dict.get(ue_id)
        if not ue or delta_time_s <= 0:
            return
        if ue.mobility_model_type == MODEL_STATIC:
            return
        elif ue.mobility_model_type == MODEL_LINEAR:
            dx = ue.speed_mps * math.cos(ue.direction_rad) * delta_time_s
            dy = ue.speed_mps * math.sin(ue.direction_rad) * delta_time_s
            new_x = ue.pos[0] + dx
            new_y = ue.pos[1] + dy
            new_x %= self.area_width
            new_y %= self.area_height
            ue.pos = (new_x, new_y)
        elif ue.mobility_model_type == MODEL_RANDOM_WAYPOINT:
            if ue.is_paused:
                ue.pause_time_s -= delta_time_s
                if ue.pause_time_s <= 0:
                    ue.is_paused = False
                    ue.current_waypoint = self._generate_random_waypoint()
                    ue.speed_mps = random.uniform(ue.min_speed_mps, ue.max_speed_mps)
                    delta_x_wp = ue.current_waypoint[0] - ue.pos[0]
                    delta_y_wp = ue.current_waypoint[1] - ue.pos[1]
                    ue.direction_rad = math.atan2(delta_y_wp, delta_x_wp)
                return
            dist_to_waypoint = math.sqrt((ue.current_waypoint[0] - ue.pos[0])**2 + (ue.current_waypoint[1] - ue.pos[1])**2)
            travel_dist = ue.speed_mps * delta_time_s
            if travel_dist >= dist_to_waypoint:
                ue.pos = ue.current_waypoint
                ue.is_paused = True
                ue.pause_time_s = random.uniform(1, 10)
            else:
                dx = ue.speed_mps * math.cos(ue.direction_rad) * delta_time_s
                dy = ue.speed_mps * math.sin(ue.direction_rad) * delta_time_s
                ue.pos = (ue.pos[0] + dx, ue.pos[1] + dy)

    def update_all_positions(self, current_sim_time_ms, last_update_time_ms):
        delta_time_s = (current_sim_time_ms - last_update_time_ms) / 1000.0
        if delta_time_s <= 0:
            return
        for ue_id in self.ues_dict.keys():
            self.update_ue_position(ue_id, delta_time_s)

if __name__ == "__main__":
    class MockLogger:
        def log_event(self, Time_ms, EventType, UE_ID, **kwargs):
            print(f"MOCK_LOG Time {Time_ms}: {EventType} by {UE_ID}, Details: {kwargs}")
    class MockSimEnv:
        def __init__(self):
            self.now = 0
    class MockUE:
        def __init__(self, ue_id):
            self.ue_id = ue_id
            self.pos = (0,0)
            self.speed_mps = 0
            self.direction_rad = 0
            self.mobility_model_type = MODEL_STATIC
            self.current_waypoint = (0,0)
            self.min_speed_mps = 0
            self.max_speed_mps = 0
            self.pause_time_s = 0
            self.is_paused = False
    logger = MockLogger()
    env = MockSimEnv()
    ue1 = MockUE("UE1")
    ue2 = MockUE("UE2")
    ues = {"UE1": ue1, "UE2": ue2}
    mobility_mgr = MobilityModel(logger, env, ues, area_dimensions=(200, 200))
    mobility_mgr.initialize_ue_state("UE1", model_type=MODEL_LINEAR, initial_pos=(10,10), initial_speed_mps=10, initial_direction_deg=45)
    mobility_mgr.initialize_ue_state("UE2", model_type=MODEL_RANDOM_WAYPOINT, initial_pos=(50,50), initial_speed_mps=5)
    ue2.min_speed_mps = 3
    ue2.max_speed_mps = 8
    last_time = env.now
    for i in range(10):
        env.now += 100
        current_state_ue1 = mobility_mgr.get_ue_state("UE1")
        current_state_ue2 = mobility_mgr.get_ue_state("UE2")
        mobility_mgr.update_all_positions(env.now, last_time)
        last_time = env.now
        print(f"Time {env.now}ms: UE1 @ {current_state_ue1['pos']}, UE2 @ {current_state_ue2['pos']} (Waypoint: {ue2.current_waypoint if ue2.mobility_model_type == MODEL_RANDOM_WAYPOINT else ''}, Paused: {ue2.is_paused if ue2.mobility_model_type == MODEL_RANDOM_WAYPOINT else ''})")
        if i == 2 and ue2.mobility_model_type == MODEL_RANDOM_WAYPOINT:
             print(f"  UE2 direction: {math.degrees(ue2.direction_rad):.2f} deg, speed: {ue2.speed_mps:.2f} m/s") 
    print("mobility.py structure defined.")

