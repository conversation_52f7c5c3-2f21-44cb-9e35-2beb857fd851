"""
碰撞關鍵訊息攻擊 (Collision of Critical Message Attack)

此攻擊方法專注於干擾特定時刻的關鍵安全訊息，如緊急煞車、變換車道等緊急情況下的BSM傳輸。
攻擊者通過監聽車輛通信，識別出緊急訊息，然後立即對該訊息所在的資源進行干擾。

攻擊步驟：
1. 監聽階段：廣泛監聽周圍車輛的BSM訊息，識別包含緊急情況指示的訊息
2. 攻擊階段：一旦識別出關鍵訊息，立即鎖定發出訊息車輛使用的資源並進行干擾
"""

import random
from src.CONFIG import *
from src.simulators.simulator import NRV2XSimulator
from src.ue import VehicleUE, AttackerUE, DEFAULT_PRSRP
from src.mobility import MODEL_LINEAR
from src.logger import Logger # Assuming Logger class is in logger.py
from src.attacker_model.critical_message_attacker import CriticalMessageAttackerUE


class Simulator2(NRV2XSimulator):
    def __init__(self, simulation_index):
        super().__init__(simulation_index)
    def setup_scenario(self):
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_START", UE_ID="System")
        print(f"Time {self.env.now}: Setting up scenario...")

        # 創建正常車輛
        for i in range(NUM_NORMAL_UES):
            ue_id = f"NormalUE-{i}"
            initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
            initial_dir_deg = 90
            initial_speed_mps = random.uniform(8, 15)
            
            # 創建UE特定日誌
            ue_logger_instance = Logger(filename=f"{ue_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
            self.ue_loggers.append(ue_logger_instance)
            
            ue = VehicleUE(self.env, ue_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                            initial_pos=initial_pos, initial_speed=initial_speed_mps,
                            prsvp=PRSVP, ue_specific_logger=ue_logger_instance)
            self.normal_ues.append(ue)
            self.all_ues.append(ue)
            self.ues_objects_dict[ue_id] = ue
            self.mobility_model.initialize_ue_state(ue_id, model_type=MODEL_LINEAR, 
                                                    initial_pos=initial_pos, 
                                                    initial_speed_mps=initial_speed_mps, 
                                                    initial_direction_deg=initial_dir_deg)
            self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=ue_id, Details="Type: Normal")
            print(f"  {ue_id} created.")

        # 創建關鍵訊息攻擊者
        for i in range(NUM_ATTACKER_UES):
            attacker_id = f"CriticalAttackerUE-{i}"
            initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
            initial_dir_deg = random.uniform(0, 360)
            initial_speed_mps = random.uniform(10, 12)
            
            # 創建攻擊者特定日誌
            attacker_logger_instance = Logger(filename=f"{attacker_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
            self.ue_loggers.append(attacker_logger_instance)
            
            # 使用CriticalMessageAttackerUE類別
            attacker = CriticalMessageAttackerUE(self.env, attacker_id, self.resource_pool, self.comm_model, 
                                                self.global_logger, self.mobility_model,
                                                initial_pos=initial_pos, initial_speed=initial_speed_mps,
                                                ue_specific_logger=attacker_logger_instance)
            self.attacker_ues.append(attacker)
            self.all_ues.append(attacker)
            self.ues_objects_dict[attacker_id] = attacker
            self.mobility_model.initialize_ue_state(attacker_id, model_type=MODEL_LINEAR, 
                                                        initial_pos=initial_pos, 
                                                        initial_speed_mps=initial_speed_mps, 
                                                        initial_direction_deg=initial_dir_deg)
            self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=attacker_id, Details=f"Type: CriticalAttacker")
            print(f"  {attacker_id} created.")

        for ue in self.all_ues:
            ue.all_other_ues = {other_ue.ue_id: other_ue for other_ue in self.all_ues if other_ue.ue_id != ue.ue_id}
            ue.comm_model = self.comm_model 
            ue.resource_pool = self.resource_pool

        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_END", UE_ID="System")
        print(f"Time {self.env.now}: Scenario setup complete. Total UEs: {len(self.all_ues)}")
    