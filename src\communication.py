# communication.py
import math
import random
from src.CONFIG import *

class CommunicationModel:
    def __init__(self, logger, resource_pool):
        self.logger = logger
        self.resource_pool = resource_pool # To get interference information
        self.noise_power_dbm = self.calculate_noise_power_dbm(REFERENCE_BANDWIDTH_HZ)
        self.noise_power_linear = self.dbm_to_linear(self.noise_power_dbm)
        
        # Make some parameters accessible for UEs if needed
        self.default_tx_power_sci = DEFAULT_TX_POWER_SCI_DBM
        self.default_tx_power_pssch = DEFAULT_TX_POWER_PSSCH_DBM
        self.sci_tx_time = SCI_TX_TIME_MS

        print(f"CommunicationModel initialized. Noise Power: {self.noise_power_dbm:.2f} dBm (for {REFERENCE_BANDWIDTH_HZ/1000} kHz BW)")

    def calculate_noise_power_dbm(self, bandwidth_hz):
        noise_power_dbm = THERMAL_NOISE_DENSITY_DBM_HZ + 10 * math.log10(bandwidth_hz) + NOISE_FIGURE_DB
        return noise_power_dbm

    def dbm_to_linear(self, dbm_value):
        """Converts dBm to linear scale (Watts)."""
        return 10**((dbm_value - 30) / 10)

    def linear_to_db(self, linear_value_watts):
        """Converts linear scale (Watts) to dBW. For dBm, add 30."""
        if linear_value_watts <= 0:
            return -float("inf") # Or handle error appropriately
        return 10 * math.log10(linear_value_watts)

    def calculate_distance(self, pos1, pos2):
        """Calculates Euclidean distance between two points (x,y)."""
        return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

    def calculate_path_loss_db(self, distance_meters):
        """Calculates path loss in dB using Free Space Path Loss model."""
        if distance_meters <= 0.1: # Avoid log(0) or very close distances; use a minimum distance
            distance_meters = 0.1 
        # FSPL (dB) = 20*log10(d) + 20*log10(f) + 20*log10(4pi/c)
        # path_loss = 20 * math.log10(distance_meters) + 20 * math.log10(FREQ_V2X_HZ) - 147.55 # (20log10(c/4pi))
        # Using pre-calculated constant for f and c part:
        path_loss = PATH_LOSS_CONSTANT + PATH_LOSS_EXPONENT * 10 * math.log10(distance_meters)
        return path_loss

    def calculate_received_power_dbm(self, tx_power_dbm, distance_meters):
        """Calculates received power in dBm after path loss."""
        if distance_meters <= 0: # Should not happen if checked before
            return tx_power_dbm # No path loss if at same exact point (or very high if error)
        
        path_loss_db = self.calculate_path_loss_db(distance_meters)
        received_power_dbm = tx_power_dbm - path_loss_db
        return received_power_dbm

    def calculate_sinr_db(self, signal_power_dbm, interference_power_linear_sum, noise_power_linear):
        """Calculates SINR in dB."""
        signal_power_linear = self.dbm_to_linear(signal_power_dbm)
        
        # Total noise + interference in linear scale
        total_noise_interference_linear = noise_power_linear + interference_power_linear_sum
        
        if total_noise_interference_linear == 0: # Avoid division by zero if no noise and no interference
            return float("inf") # Effectively infinite SINR
            
        sinr_linear = signal_power_linear / total_noise_interference_linear
        
        if sinr_linear <= 0: # Can happen if signal is overwhelmed or due to float precision
            return -float("inf")
            
        sinr_db = 10 * math.log10(sinr_linear)
        return sinr_db

    def is_decoded_successfully(self, sinr_db, signal_type):
        """Determines if a signal is decoded successfully based on SINR threshold."""
        threshold = 0
        if signal_type == "SCI":
            threshold = SINR_THRESHOLD_SCI_DB
        elif signal_type == "PSSCH":
            threshold = SINR_THRESHOLD_PSSCH_DB # This should be MCS dependent
        else:
            self.logger.log_event(-1, "COMM_UNKNOWN_SIG_TYPE_DECODE", "System", details=f"Unknown signal type: {signal_type}")
            return False
        
        return sinr_db >= threshold

    def calculate_pssch_tx_time(self, data_packet):
        """Calculates PSSCH transmission time based on data size (simplified)."""
        # data_packet could be a BSM dict, or just an abstract size
        # For simplicity, let data_packet be the BSM dict from ue.py
        # A more realistic model would use TBS, MCS, number of RBs, etc.
        num_bytes = len(str(data_packet)) # Very rough estimate of size
        time_ms = PSSCH_BASE_TX_TIME_MS + num_bytes * PSSCH_TIME_PER_BYTE_MS
        # Ensure it fits within typical slot durations or a max allowed time
        # return max(PSSCH_BASE_TX_TIME_MS, min(time_ms, 10 * PSSCH_BASE_TX_TIME_MS)) # Cap duration
        return PSSCH_BASE_TX_TIME_MS

    def get_all_interferers_and_jammers(self, env_time, target_resource, exclude_ue_id):
        """ Get all active interfering transmissions and jamming signals on a resource at a given time. """
        interferers = [] # List of (ue_id, tx_power_dbm, distance_to_rx_ue)
        # This function will be called by a receiving UE. It needs the receiver's position to calculate distance to interferers.
        # For now, the resource_pool.get_interference_on_resource is a simpler sum.
        # This function aims to provide a list for the UE to calculate individual received powers from interferers.

        # Check concurrent transmissions from other UEs in resource_pool.active_transmissions
        for trans in self.resource_pool.active_transmissions:
            if trans["resource"] == target_resource and trans["ue_id"] != exclude_ue_id and \
               trans["start_time"] <= env_time < trans["end_time"]:
                interferers.append({
                    "id": trans["ue_id"],
                    "tx_power_dbm": trans["tx_power"],
                    "type": "transmission"
                })
        
        # Check concurrent jamming in resource_pool.active_jamming
        for jam in self.resource_pool.active_jamming:
            if jam["resource"] == target_resource and jam["ue_id"] != exclude_ue_id and \
               jam["start_time"] <= env_time < jam["end_time"]:
                 interferers.append({
                    "id": jam["ue_id"],
                    "tx_power_dbm": jam["power"], # Jamming power
                    "type": "jamming"
                })
        return interferers

# Example Usage (for testing this file independently)
if __name__ == "__main__":
    class MockLogger:
        def log_event(self, time, event_type, ue_id, **kwargs):
            print(f"MOCK_LOG Time {time}: {event_type} by {ue_id}, Details: {kwargs}")
    
    class MockResourcePool: # Simplified for this test
        def __init__(self):
            self.active_transmissions = []
            self.active_jamming = []

    logger = MockLogger()
    rp = MockResourcePool()
    comm_model = CommunicationModel(logger, rp)

    tx_power = 23.0  # dBm
    distance = 100.0  # meters

    # Test path loss and received power
    pl_db = comm_model.calculate_path_loss_db(distance)
    print(f"Path Loss at {distance}m: {pl_db:.2f} dB")
    rx_power_dbm = comm_model.calculate_received_power_dbm(tx_power, distance)
    print(f"Received Power from Tx {tx_power} dBm at {distance}m: {rx_power_dbm:.2f} dBm")

    # Test SINR calculation
    # Assume some interference power sum in linear watts
    interference_linear_watts = comm_model.dbm_to_linear(0) # e.g., 0 dBm interference = 1mW
    sinr = comm_model.calculate_sinr_db(rx_power_dbm, interference_linear_watts, comm_model.noise_power_linear)
    print(f"SINR: {sinr:.2f} dB (with {10*math.log10(interference_linear_watts)+30:.1f} dBm interference and {comm_model.noise_power_dbm:.1f} dBm noise)")

    # Test decoding
    decoded_sci = comm_model.is_decoded_successfully(sinr, "SCI")
    print(f"SCI Decoded: {decoded_sci} (Threshold: {SINR_THRESHOLD_SCI_DB} dB)")
    decoded_pssch = comm_model.is_decoded_successfully(sinr, "PSSCH")
    print(f"PSSCH Decoded: {decoded_pssch} (Threshold: {SINR_THRESHOLD_PSSCH_DB} dB)")

    # Test PSSCH tx time
    sample_bsm = {"id": 123, "ue_id": "UE-Test", "timestamp": 1000, "pos": (10,20), "speed": 15}
    pssch_time = comm_model.calculate_pssch_tx_time(sample_bsm)
    print(f"Calculated PSSCH Tx Time for sample BSM: {pssch_time} ms")

    print("communication.py structure defined.")

