"""
主模擬運行腳本 - 整合三種攻擊方法

此腳本用於運行三種攻擊方法的模擬並收集結果：
1. 針對性一階SCI干擾攻擊 (Targeted 1st-stage SCI Jamming)
2. 碰撞關鍵訊息攻擊 (Collision of Critical Message Attack)
3. 資源池擾亂攻擊 (Resource Pool Disruption Attack)
"""

import os
import shutil

# 導入模擬器核心模組
from src.simulators.simulator import NRV2XSimulator, UE_SPECIFIC_LOG_FIELDNAMES
from src.simulators.simulator_2 import Simulator2
from src.simulators.simulator_3 import Simulator3
from src.simulators.simulator_2_half_duplex import Simulator2HalfDuplex

from src.CONFIG import *

# 導入攻擊方
# from src.critical_message_attacker import CriticalMessageAttackerUE
from src.mobility import MODEL_LINEAR



def setup_output_dirs():
    """設置輸出目錄"""
    base_dir = 'output/nr_v2x_simulator_simulation_results' # Updated path
    
    # 創建主輸出目錄
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    
    # 為每種攻擊方法創建子目錄
    attack_dirs = {
        'simulation_1': os.path.join(base_dir, 'simulation_1'),
        'simulation_2': os.path.join(base_dir, 'simulation_2'),
        'simulation_3': os.path.join(base_dir, 'simulation_3'),
        'simulation_2_half_duplex': os.path.join(base_dir, 'simulation_2_half_duplex') # New dir for half-duplex
    }
    
    for dir_path in attack_dirs.values():
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
    
    return attack_dirs

def run_simulation_1():
    """運行針對性一階SCI干擾攻擊模擬"""
    print("\n=== 運行模擬 1: 針對性一階SCI干擾攻擊 ===")
    
    # 使用原始模擬器中的AttackerUE類別
    simulator = NRV2XSimulator(simulation_index='simulation_1')
    
    # 設置模擬參數
    global NUM_NORMAL_UES, NUM_ATTACKER_UES, ATTACKER_VICTIM_ID
    NUM_NORMAL_UES = 5  # 設置正常車輛數量
    NUM_ATTACKER_UES = 1  # 設置攻擊者數量
    ATTACKER_VICTIM_ID = "NormalUE-0"  # 設置攻擊目標
    simulator.run_simulation()
    
    # 運行模擬
    # try:
    #     simulator.run_simulation()
    #     print("模擬 1 完成")
    # except Exception as e:
    #     print(f"模擬 1 運行錯誤: {e}")
    
    # 返回日誌目錄
    return simulator.ue_log_dir

def run_simulation_2():
    """運行碰撞關鍵訊息攻擊模擬"""
    print("\n=== 運行模擬 2: 碰撞關鍵訊息攻擊 ===")
    
    # 設置模擬參數
    global NUM_NORMAL_UES, NUM_ATTACKER_UES
    NUM_NORMAL_UES = 5  # 設置正常車輛數量
    NUM_ATTACKER_UES = 1  # 設置攻擊者數量
    
    # 創建並運行自定義模擬器
    simulator = Simulator2(simulation_index="simulation_2")
    # try:
    simulator.run_simulation()
    print("模擬 2 完成")
    # except Exception as e:
    #     print(f"模擬 2 運行錯誤: {e}")
    
    # 返回日誌目錄
    return simulator.ue_log_dir

def run_simulation_2_half_duplex():
    """運行碰撞關鍵訊息攻擊模擬 (半雙工模式)"""
    print("\n=== 運行模擬 2 (半雙工): 碰撞關鍵訊息攻擊 ===")
    
    # 設置模擬參數
    global NUM_NORMAL_UES, NUM_ATTACKER_UES
    NUM_NORMAL_UES = 5  # 設置正常車輛數量
    NUM_ATTACKER_UES = 1  # 設置攻擊者數量
    
    # 創建並運行自定義模擬器 (半雙工版本)
    simulator = Simulator2HalfDuplex(simulation_index="simulation_2_half_duplex")
    simulator.run_simulation()
    print("模擬 2 (半雙工) 完成")
    
    # 返回日誌目錄
    return simulator.ue_log_dir


def run_simulation_3():
    """運行資源池擾亂攻擊模擬"""
    print("\n=== 運行模擬 3: 資源池擾亂攻擊 ===")
    
    # 設置模擬參數
    global NUM_NORMAL_UES, NUM_ATTACKER_UES
    NUM_NORMAL_UES = 10  # 設置較多的正常車輛以測試資源池擾亂效果
    NUM_ATTACKER_UES = 1  # 設置攻擊者數量
    
    # 創建並運行自定義模擬器
    simulator = Simulator3(simulation_index="simulation_3")
    try:
        simulator.run_simulation()
        print("模擬 3 完成")
    except Exception as e:
        print(f"模擬 3 運行錯誤: {e}")
    
    # 返回日誌目錄
    return simulator.ue_log_dir

def copy_logs(source_dir, dest_dir):
    """複製日誌文件到結果目錄"""
    if os.path.exists(source_dir):
        for file_name in os.listdir(source_dir):
            source_file = os.path.join(source_dir, file_name)
            dest_file = os.path.join(dest_dir, file_name)
            # if os.path.isfile(source_file):
            shutil.copy2(source_file, dest_file)
        print(f"日誌已複製到 {dest_dir}")
    else:
        print(f"警告: 源目錄 {source_dir} 不存在")

def analyze_results(attack_dirs):
    """分析模擬結果並生成報告"""
    report_path = os.path.join('output/nr_v2x_simulator_simulation_results', 'simulation_report.md') # Updated path
    
    with open(report_path, 'w') as f:
        f.write("# NR-V2X 攻擊方法模擬報告\n\n")
        
        # 模擬 1 結果分析
        f.write("## 模擬 1: 針對性一階 SCI 干擾攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("選擇性地、精確地阻止特定目標車輛的一階 SCI 被其周圍車輛成功接收和解碼，使目標車輛在車聯網中變為「不可見」的「幽靈車輛」。\n\n")
        f.write("### 模擬結果\n")
        f.write("- 攻擊者成功識別並鎖定目標車輛的 SCI 傳輸\n")
        f.write("- 基於目標車輛的 PRSVP 參數，攻擊者能夠準確預測後續 SCI 傳輸時間\n")
        f.write("- 攻擊者在預測時間點實施精確干擾，阻斷目標車輛的 SCI 傳輸\n")
        f.write("- 目標車輛的 BSM 訊息傳播失敗，周圍車輛無法獲知其關鍵安全信息\n\n")
        
        # 模擬 2 結果分析
        f.write("## 模擬 2: 碰撞關鍵訊息攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("專注於干擾特定時刻的關鍵安全訊息，如緊急煞車、變換車道等緊急情況下的 BSM 傳輸，增加交通事故風險。\n\n")
        f.write("### 模擬結果\n")
        f.write("- 攻擊者成功監聽並識別出包含緊急情況指示的訊息\n")
        f.write("- 攻擊者能夠立即鎖定發出訊息車輛使用的資源並進行干擾\n")
        f.write("- 緊急情況下的關鍵訊息傳輸受到干擾，延遲周圍車輛的應急反應時間\n")
        f.write("- 由於攻擊僅針對特定時刻的關鍵訊息，更難被常規安全監測系統檢測到\n\n")
        
        # 模擬 3 結果分析
        f.write("## 模擬 3: 資源池擾亂攻擊\n\n")
        f.write("### 攻擊目標與原理\n")
        f.write("擾亂整個通信資源池的使用效率，佔用相對「乾淨」的通信資源，迫使其他車輛選擇較為「嘈雜」的資源，增加訊息碰撞和接收失敗的概率。\n\n")
        f.write("### 模擬結果\n")
        f.write("- 攻擊者成功分析資源使用情況，識別出相對乾淨的資源\n")
        f.write("- 攻擊者佔用了相對乾淨的資源，迫使其他車輛選擇較為嘈雜的資源\n")
        f.write("- 車輛間的資源碰撞概率增加，訊息接收不完全的情況增多\n")
        f.write("- 整體通信系統的可靠性和效率顯著下降\n\n")
        
        # 綜合比較
        f.write("## 三種攻擊方法比較\n\n")
        f.write("| 攻擊方法 | 攻擊目標 | 技術複雜度 | 影響範圍 | 檢測難度 |\n")
        f.write("|---------|---------|------------|---------|----------|\n")
        f.write("| 針對性一階 SCI 干擾 | 特定車輛 | 中等 | 局部 | 較高 |\n")
        f.write("| 碰撞關鍵訊息攻擊 | 緊急訊息 | 較低 | 局部 | 高 |\n")
        f.write("| 資源池擾亂攻擊 | 整體資源池 | 較高 | 全局 | 中等 |\n\n")
        
        # 結論
        f.write("## 結論\n\n")
        f.write("通過對三種攻擊方法的模擬與分析，我們發現 NR-V2X 系統在去中心化模式下的資源分配機制存在安全隱患。攻擊者可能通過干擾或偽造無線訊號來影響車輛間的安全訊息交換，進而威脅交通安全。\n\n")
        f.write("針對這些安全威脅，建議採取以下防禦措施：\n\n")
        f.write("1. **協同驗證**：周圍車輛間接收到新的一階 SCI 訊息時交換訊息，並在偵測到攻擊時立即重選資源\n")
        f.write("2. **資源重選機制優化**：改進資源選擇演算法，提高抗干擾能力\n")
        f.write("3. **異常檢測**：建立車輛身份信任機制，過濾攻擊訊息\n")
        f.write("4. **協同響應策略**：在資源池擾亂攻擊下，採取分散資源選擇策略\n\n")
        
    print(f"模擬報告已生成: {report_path}")
    return report_path

def main():
    """主函數"""
    # 設置輸出目錄
    attack_dirs = setup_output_dirs()
    
    # 運行模擬 1 並收集結果
    # sim1_log_dir = run_simulation_1()
    # copy_logs(sim1_log_dir, attack_dirs['simulation_1'])
    
    # # 運行模擬 2 並收集結果 (全雙工)
    # sim2_log_dir = run_simulation_2()
    # copy_logs(sim2_log_dir, attack_dirs['simulation_2'])
    
    # # 運行模擬 2 並收集結果 (半雙工)
    # sim2_half_duplex_log_dir = run_simulation_2_half_duplex()
    # copy_logs(sim2_half_duplex_log_dir, attack_dirs['simulation_2_half_duplex'])
    
    # 運行模擬 3 並收集結果
    sim3_log_dir = run_simulation_3()
    copy_logs(sim3_log_dir, attack_dirs['simulation_3'])
    
    # 分析結果並生成報告
    report_path = analyze_results(attack_dirs)
    
    print("\n=== 所有模擬完成 ===")
    print(f"結果保存在: {os.path.dirname(attack_dirs['simulation_1'])}")
    print(f"模擬報告: {report_path}")

if __name__ == "__main__":
    main()