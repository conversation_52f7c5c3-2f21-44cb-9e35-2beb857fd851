"""
碰撞關鍵訊息攻擊 (Collision of Critical Message Attack) - 半雙工版本

此攻擊方法專注於干擾特定時刻的關鍵安全訊息，如緊急煞車、變換車道等緊急情況下的BSM傳輸。
攻擊者通過監聽車輛通信，識別出緊急訊息，然後立即對該訊息所在的資源進行干擾。

攻擊步驟：
1. 監聽階段：廣泛監聽周圍車輛的BSM訊息，識別包含緊急情況指示的訊息
2. 攻擊階段：一旦識別出關鍵訊息，立即鎖定發出訊息車輛使用的資源並進行干擾
   在干擾期間，攻擊者無法接收任何訊息（半雙工模式）。
"""

import random
from src.CONFIG import *

class HalfDuplexCriticalMessageAttackerUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model, 
                 initial_pos=(0,0), initial_speed=0, 
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM, jamming_duration_ms=ATTACKER_JAMMING_DURATION_MS,
                 ue_specific_logger=None):
        # 繼承基本UE類別的初始化
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger
        self.ue_logger = ue_specific_logger
        self.mobility_model = mobility_model
        
        self.pos = initial_pos
        self.speed = initial_speed
        self.direction_rad = 0
        
        self.prsvp = DEFAULT_PRSRP
        self.slrrc = 0 
        self.slrrc_max = DEFAULT_SLRRC_MAX 
        self.slrrc_min = DEFAULT_SLRRC_MIN
        self.nmax_reserve = DEFAULT_NMAX_RESERVE
        self.resource_keep_probability = DEFAULT_RESOURCE_KEEP_PROBABILITY
        
        self.selected_resources = [] 
        self.sensed_resource_map = {} 
        self.data_queue = [] 
        self.is_attacker = True
        self.all_other_ues = {}
        
        # 攻擊者特有參數
        self.jamming_power_dbm = jamming_power_dbm
        self.jamming_duration_ms = jamming_duration_ms
        self.critical_message_types = ["EMERGENCY_BRAKE", "LANE_CHANGE", "COLLISION_WARNING"]
        self.monitored_ues = {}  # 記錄監聽到的車輛及其資源使用情況
        self.attack_cooldown = 0  # 攻擊冷卻時間

        # 半雙工相關狀態
        self.is_jamming = False # 新增：標記攻擊者是否正在干擾

        self.global_logger.log_event(Time_ms=self.env.now, EventType="CRITICAL_ATTACKER_INIT", UE_ID=self.ue_id, 
                              Details=f"Jamming Power: {self.jamming_power_dbm}dBm. Monitoring for critical messages.")
        
        # 啟動攻擊者的運行邏輯
        self.action = env.process(self._monitor_and_trigger_jams())
        self.receiver_process_action = env.process(self.receiver_process())
        self.victim_record = {} #{ue_id: {"resource": resource, "jam_process": None}}
        
    def _monitor_and_trigger_jams(self):
            """攻擊者的主要運行邏輯，現在只負責執行干擾"""
            self.global_logger.log_event(Time_ms=self.env.now, EventType="CRITICAL_ATTACKER_LOGIC_START", UE_ID=self.ue_id)
            if self.ue_logger:
                self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="CRITICAL_ATTACKER_PROCESS_START")
            
            while True:
                for ue_id, victim_info in list(self.victim_record.items()):
                    if victim_info["jam_process"] is None or not victim_info["jam_process"].is_alive:
                        # 如果沒有正在運行的干擾進程，則啟動一個新的
                        victim_info["jam_process"] = self.env.process(self._jam_victim_sequence(ue_id, victim_info["resource"]))
                
                # 仍然以 1ms 的頻率檢查，以確保能精準地在 slot 開始時干擾
                yield self.env.timeout(SLOT_DURATION_MS)
    
    def _jam_victim_sequence(self, target_ue_id, target_resource):
        jam_count = 0
        
        # 計算第一次干擾的等待時間，以對齊目標資源的時隙
        initial_current_time = self.env.now
        current_slot_in_frame = initial_current_time % 100
        target_slot_in_frame = target_resource[0] # 這是 100ms 幀內的時隙索引

        time_to_first_jam = 0
        if target_slot_in_frame >= current_slot_in_frame:
            time_to_first_jam = target_slot_in_frame - current_slot_in_frame
        else:
            time_to_first_jam = (100 - current_slot_in_frame) + target_slot_in_frame

        if time_to_first_jam > 0:
            yield self.env.timeout(time_to_first_jam)

        # 執行第一次干擾
        yield self.env.process(self.execute_jamming(target_ue_id, target_resource))
        jam_count += 1

        # 對於剩餘的干擾，每次等待 PRSRP 毫秒
        while jam_count < 5:
            # 每次干擾後，execute_jamming 會使 self.env.now 推進 SLOT_DURATION_MS (1ms)
            # 因此，我們需要再等待 (PRSRP - SLOT_DURATION_MS) 毫秒，以確保下一次干擾在 PRSRP 週期後發生
            yield self.env.timeout(PRSVP - SLOT_DURATION_MS)
            if target_ue_id == "NormalUE-1":
                print(target_ue_id, target_resource)
            yield self.env.process(self.execute_jamming(target_ue_id, target_resource))
            jam_count += 1

        # 攻擊次數達到上限，移除受害者
        self.global_logger.log_event(Time_ms=self.env.now, EventType="CRITICAL_ATTACKER_JAM_LIMIT_REACHED", 
                                      UE_ID=self.ue_id, Target_UE_ID=target_ue_id, 
                                      Resource=str(target_resource), Details="5 jams completed. Removing victim.")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, 
                                    EventType="CRITICAL_ATTACKER_JAM_LIMIT_REACHED",
                                    Related_UE_ID=target_ue_id, 
                                    Resource_Details=str(target_resource), 
                                    Details="5 jams completed. Removing victim.")
        # 從 victim_record 中移除該受害者
        if target_ue_id in self.victim_record:
            del self.victim_record[target_ue_id]
    
    def execute_jamming(self, target_ue_id, target_resource):
        """執行對關鍵訊息的干擾，並模擬半雙工行為"""
        current_time = self.env.now
        
        self.global_logger.log_event(Time_ms=current_time, EventType="CRITICAL_ATTACKER_JAM_START", 
                              UE_ID=self.ue_id, Target_UE_ID=target_ue_id, 
                              Resource=str(target_resource), Power_dBm=self.jamming_power_dbm,
                              Details=f"Jamming critical message")
        
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                                    EventType="CRITICAL_ATTACKER_JAM_EXECUTE", 
                                    Resource_Details=str(target_resource), 
                                    Related_UE_ID=target_ue_id, 
                                    Details=f"Power: {self.jamming_power_dbm}dBm, Message")
        
        # 設定干擾標誌為 True，表示攻擊者正在發送，無法接收
        self.is_jamming = True

        # 對目標資源進行干擾
        if target_resource:
            self.resource_pool.log_jamming(self.ue_id, target_resource, self.jamming_power_dbm, self.jamming_duration_ms)
        
        # 等待一個時隙。在此期間，receiver_process 將會「盲」
        yield self.env.timeout(SLOT_DURATION_MS)

        # 干擾結束後，重置干擾標誌為 False，攻擊者可以再次接收
        self.is_jamming = False

        self.global_logger.log_event(Time_ms=self.env.now, EventType="CRITICAL_ATTACKER_JAM_END", 
                              UE_ID=self.ue_id, Target_UE_ID=target_ue_id, 
                              Resource=str(target_resource), Details="Jamming ended.")
    
    def receiver_process(self):
        """接收器處理，監聽周圍車輛的訊息"""
        while True:
            # 如果攻擊者正在干擾，則跳過接收邏輯，等待下一個輪詢間隔
            if self.is_jamming:
                yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
                continue

            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
            current_time = self.env.now
            
            # 更新自身位置和速度
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]
            self.speed = current_mobility_state["speed_mps"]
            
            # 獲取當前活躍的傳輸
            active_transmissions_copy = dict(self.resource_pool.active_transmissions)
            
            for trans_key, trans_info in active_transmissions_copy.items():
                if not (trans_info["start_time"] <= current_time <= trans_info["end_time"]):
                    continue
                
                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id:
                    continue
                
                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]
                signal_type = trans_info["type"]
                content = trans_info["content"]
                # if sender_id == "NormalUE-1":
                #     print(sender_id, resource,signal_type, content)
                
                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object:
                    continue
                
                sender_pos = sender_ue_object.pos
                
                # 計算距離並檢查是否在通信範圍內
                distance = self.comm_model.calculate_distance(self.pos, sender_pos)
                if distance > MAX_COMM_RANGE_M:
                    continue
                
                # 計算接收功率並檢查是否超過最小接收閾值
                received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if received_power_dbm < MIN_RX_POWER_DBM:
                    continue
                
                # 更新監聽到的車輛信息
                if sender_id not in self.monitored_ues:
                    self.monitored_ues[sender_id] = {"resource": None, "last_message": None}
                
                # 更新資源使用情況 - 確保資源是tuple類型
                self.monitored_ues[sender_id]["resource"] = resource
                # 更新最近接收到的訊息
                if signal_type == "PSSCH" and content:
                    is_critical = content.get("is_emergency")
                    if is_critical == True:
                        
                        # 如果是緊急訊息，立即更新受害者記錄
                        # 只有在目標改變時才打印日誌，避免刷屏
                        # 如果是緊急訊息，立即更新受害者記錄
                        # 只有在目標改變時才打印日誌，避免刷屏
                        if self.victim_record.get(sender_id) is None or self.victim_record[sender_id]["resource"] != resource:
                            self.global_logger.log_event(Time_ms=current_time, EventType="CRITICAL_ATTACKER_TARGET_DETECTED", 
                                                          UE_ID=self.ue_id, Target_UE_ID=sender_id,
                                                          Details=f"Critical message detected on resource {resource}!",
                                                          PSSCH_Content_Abstract=str(content))
                            if self.ue_logger:
                                self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                                                        EventType="CRITICAL_ATTACKER_TARGET_DETECTED",
                                                        Related_UE_ID=sender_id, 
                                                        Details=f"Critical message detected!",
                                                        PSSCH_Content_Abstract=str(content))
                        
                        self.victim_record[sender_id] = {"resource": resource, "jam_process": None}
                    elif is_critical == False:
                        self.victim_record.pop(sender_id, None)
                    

                    # self.monitored_ues[sender_id]["last_message"] = content
                    
                    # # 記錄監聽到的訊息
                    # self.global_logger.log_event(Time_ms=current_time, EventType="CRITICAL_ATTACKER_MESSAGE_MONITORED", 
                    #                       UE_ID=self.ue_id, Target_UE_ID=sender_id,
                    #                       Content_Abstract=str(content))
                    
                    # if self.ue_logger:
                    #     self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id, 
                    #                             EventType="CRITICAL_ATTACKER_MESSAGE_MONITORED", 
                    #                             Related_UE_ID=sender_id, 
                    #                             PSSCH_Content_Abstract=str(content))
