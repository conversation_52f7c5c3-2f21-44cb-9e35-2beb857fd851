# resource_pool.py
import simpy
import random # Added for get_candidate_resources
import math   # Added for get_current_slot_index and get_candidate_resources
from src.CONFIG import *

class ResourcePool:
    def __init__(self, env, logger):
        self.env = env
        self.logger = logger
        self.resources = {} 
        self.active_transmissions = {} 
        self.active_jamming = {}
        self.reservations_map = {}
        print(f"ResourcePool initialized with {NUM_SUBCHANNELS} sub-channels per slot.")

    def get_current_slot_index(self, current_time_ms=None):
        if current_time_ms is None:
            current_time_ms = self.env.now
        return math.floor(current_time_ms / SLOT_DURATION_MS)

    def reserve_resource(self, ue_id, resource_tuple, prsvp, slrrc_max_val, reservation_start_time):
        # if ue_id == "NormalUE-0":
        #     print(f"Reservation for {ue_id} on {resource_tuple}")
        reservation_absolute_end_time = reservation_start_time + (slrrc_max_val * prsvp)
        self.reservations_map[resource_tuple] = {
            "ue_id": ue_id,
            "prsvp": prsvp,
            "slrrc_max": slrrc_max_val,
            "reservation_start_time": reservation_start_time,
            "reservation_absolute_end_time": reservation_absolute_end_time
        }
        self.logger.log_event(Time_ms=self.env.now, EventType="RP_SPS_RESERVATION_MADE", UE_ID=ue_id, Resource=str((resource_tuple[0] % 100, resource_tuple[1])), 
                              Details=f"Prsvp:{prsvp}, EndTime:{reservation_absolute_end_time}")

    def release_reservation(self, ue_id, resource_tuple, time):
        time = time // 100
        resource_tuple = (resource_tuple[0] + (time * 100), resource_tuple[1])
        # if ue_id == "NormalUE-0":
        #     print(f"Releasing reservation for {ue_id} on {resource_tuple}")
        if resource_tuple in self.reservations_map and self.reservations_map[resource_tuple]["ue_id"] == ue_id:
            del self.reservations_map[resource_tuple]
            self.logger.log_event(Time_ms=self.env.now, EventType="RP_SPS_RESERVATION_RELEASED", UE_ID=ue_id, Resource=str(resource_tuple))
        else:
            self.logger.log_event(Time_ms=self.env.now, EventType="RP_SPS_RELEASE_FAIL", UE_ID=ue_id, Resource=str(resource_tuple), Details="Reservation not found or not owned by UE")

    def log_transmission(self, ue_id, resource_tuple, signal_type, content, tx_power, duration, is_control=False):
        transmission_start_time = self.env.now
        transmission_end_time = transmission_start_time + duration
        channel = "PSCCH" if signal_type == "SCI" else "PSSCH"
        trans_key = (ue_id, resource_tuple, transmission_start_time, channel)
        
        self.active_transmissions[trans_key] = {
            "ue_id": ue_id, 
            "resource": resource_tuple, 
            "type": signal_type, 
            "content": content, 
            "tx_power": tx_power, 
            "start_time": transmission_start_time,
            "end_time": transmission_end_time
        }
        self.logger.log_event(Time_ms=transmission_start_time, EventType=f"RP_{signal_type}_TX_LOGGED", UE_ID=ue_id, Resource=str(resource_tuple), 
                              Details=f"Power {tx_power:.1f}dBm, Ends at {transmission_end_time:.2f}, Dur:{duration:.2f}ms")
        self.env.process(self._clear_active_event(trans_key, transmission_end_time, is_jamming=False))

    def log_jamming(self, ue_id, resource_tuple, power, duration):
        jam_start_time = self.env.now
        jam_end_time = jam_start_time + duration
        jam_key = (ue_id, resource_tuple, jam_start_time)

        self.active_jamming[jam_key] = {
            "ue_id": ue_id, 
            "resource": resource_tuple, 
            "power": power, 
            "start_time": jam_start_time,
            "end_time": jam_end_time
        }
        #print("Jamming logged for UE", ue_id, "on resource", resource_tuple, "with power", power, "for duration", duration)
        self.logger.log_event(Time_ms=jam_start_time, EventType="RP_JAMMING_LOGGED", UE_ID=ue_id, Resource=str(resource_tuple), 
                              Details=f"Power {power}dBm, Ends at {jam_end_time:.2f}, Dur:{duration:.2f}ms")
        self.env.process(self._clear_active_event(jam_key, jam_end_time, is_jamming=True))

    def _clear_active_event(self, event_key, end_time, is_jamming):
        yield self.env.timeout(end_time - self.env.now) 
        current_time = self.env.now
        active_map = self.active_jamming if is_jamming else self.active_transmissions
        if event_key in active_map:
            if active_map[event_key]["end_time"] <= current_time + 0.01:
                del active_map[event_key]
                event_type_str = "JAMMING_EVENT_CLEARED" if is_jamming else "TRANSMISSION_EVENT_CLEARED"
                self.logger.log_event(Time_ms=current_time, EventType=f"RP_{event_type_str}", UE_ID=event_key[0], Resource=str(event_key[1]), Details=f"Key: {event_key}")

    def get_candidate_resources(self, requesting_ue_id, sensed_scis_content_list, x_percent, current_time_ms):
        selection_window_start_slot = self.get_current_slot_index(current_time_ms) + DEFAULT_SELECTION_WINDOW_T1_SLOTS
        selection_window_end_slot = self.get_current_slot_index(current_time_ms) + DEFAULT_SELECTION_WINDOW_T2_SLOTS
        
        excluded_by_sensing = set()
        for sci_content in sensed_scis_content_list:
            announced_pssch_res = sci_content.get("pssch_resource")
            sender_prsvp = sci_content.get("prsvp")
            sci_gen_time = sci_content.get("timestamp")

            if announced_pssch_res and sender_prsvp is not None and sci_gen_time is not None:
                abs_future_tx_time = sci_gen_time + sender_prsvp
                abs_future_tx_slot = self.get_current_slot_index(abs_future_tx_time)
                
                if selection_window_start_slot <= abs_future_tx_slot <= selection_window_end_slot:
                    excluded_by_sensing.add((abs_future_tx_slot, announced_pssch_res[1]))
        
        available_resources = []
        for t_slot in range(selection_window_start_slot, selection_window_end_slot + 1):
            for s_chan in range(NUM_SUBCHANNELS):
                key = (t_slot, s_chan)
                if key in excluded_by_sensing:
                    continue

                is_reserved_by_other = False
                for res_start_key_tuple, reservation_details in self.reservations_map.items():
                    if reservation_details["ue_id"] == requesting_ue_id: 
                        continue
                    
                    if res_start_key_tuple[1] == s_chan:
                        prsvp_ms = reservation_details["prsvp"]
                        if SLOT_DURATION_MS <= 0 or prsvp_ms <= 0: 
                            continue 
                        prsvp_in_slots = prsvp_ms // SLOT_DURATION_MS
                        if prsvp_in_slots == 0: 
                            if t_slot == res_start_key_tuple[0] and reservation_details["slrrc_max"] > 0:
                                is_reserved_by_other = True
                                break
                            continue
                        if t_slot >= res_start_key_tuple[0] and \
                           (t_slot - res_start_key_tuple[0]) % prsvp_in_slots == 0:
                            k_value = (t_slot - res_start_key_tuple[0]) // prsvp_in_slots
                            if 0 <= k_value < reservation_details["slrrc_max"]:
                                is_reserved_by_other = True
                                break
                if is_reserved_by_other:
                    continue
                available_resources.append(key)
        
        if not available_resources:
            self.logger.log_event(Time_ms=current_time_ms, EventType="RP_NO_CANDIDATES_FOUND", UE_ID=requesting_ue_id, Details=f"Win:[{selection_window_start_slot}-{selection_window_end_slot}], Excl_sense:{len(excluded_by_sensing)}")
            return []

        num_target_candidates = math.ceil(len(available_resources) * x_percent) 
        if not num_target_candidates and available_resources: num_target_candidates = 1
        
        if len(available_resources) <= num_target_candidates:
            candidates = available_resources
        else:
            candidates = random.sample(available_resources, int(num_target_candidates))
        
        self.logger.log_event(Time_ms=current_time_ms, EventType="RP_CANDIDATES_SELECTED", UE_ID=requesting_ue_id, Details=f"{len(candidates)} from {len(available_resources)} available in win [{selection_window_start_slot}-{selection_window_end_slot}]")
        return candidates

    def get_activity_in_window(self, start_time, end_time):
        activities = []
        for trans_key, trans_info in self.active_transmissions.items():
            if trans_info["start_time"] < end_time and trans_info["end_time"] > start_time:
                activities.append(trans_info)
        return activities

    def get_scis_from_ue(self, target_ue_id, start_time, end_time):
        victim_scis = []
        for trans_key, trans_info in self.active_transmissions.items():
            if trans_info["ue_id"] == target_ue_id and trans_info["type"] == "SCI" and \
               trans_info["start_time"] < end_time and trans_info["end_time"] > start_time:
                victim_scis.append(trans_info) 
        return victim_scis

    def dbm_to_linear(self, dbm):
        return 10**((dbm - 30) / 10)

if __name__ == '__main__':
    print("resource_pool.py structure defined and potentially updated.")

